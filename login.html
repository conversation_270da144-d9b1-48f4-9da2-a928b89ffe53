<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="styles/login.css" />
  </head>
  <body class="login-body">
    <div class="login-container">
      <div class="login-box">
        <div class="login-header">
          <div class="logo">
            <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1747737528/logo_i76d4f.jpg" style="width: 50px; height: 50px;" alt="Logo" class="sidebar-logo">
          <!--<img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1747739605/Pulse_2_wyagyh.png" style="width: 30%; height: 30%;" alt="Logo" class="sidebar-logo">-->
          </div>
          <h2>Welcome Back</h2>
          <p class="text-muted">Please login to your account</p>
        </div>
        <form id="login-form">
          <div class="form-group">
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text">
                  <i class="fas fa-user"></i>
                </span>
              </div>
              <input
                type="text"
                class="form-control"
                id="login-username"
                name="username"
                placeholder="Username"
                required
              />
            </div>
          </div>
          <div class="form-group">
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text">
                  <i class="fas fa-lock"></i>
                </span>
              </div>
              <input
                type="password"
                class="form-control"
                id="login-password"
                name="password"
                placeholder="Password"
                required
              />
              <div class="input-group-append">
                <span class="input-group-text toggle-password" onclick="togglePassword()">
                  <i class="fas fa-eye"></i>
                </span>
              </div>
            </div>
          </div>
          <button type="submit" class="btn btn-primary btn-block login-btn">
            <span>Login</span>
            <i class="fas fa-arrow-right"></i>
          </button>
        </form>
        <div class="login-footer">
          <!--<p>Don't have an account? <a href="/Registration.html">Register here</a></p>-->
        </div>
      </div>
    </div>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
      document.getElementById("login-form").addEventListener("submit", handleLogin);

      async function handleLogin(event) {
        event.preventDefault();
        const username = document.getElementById("login-username").value;
        const password = document.getElementById("login-password").value;

        try {
          const response = await fetch("/api/login", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ username, password }),
          });

          if (response.ok) {
            const { token, role, name, avatarUrl } = await response.json();
            localStorage.setItem("token", token);
            localStorage.setItem("role", role);
            localStorage.setItem("username", name || username);
            // Ensure we always set a valid avatar URL
            localStorage.setItem("userAvatar", avatarUrl || 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp');
            
            // Verify the avatar was set
            console.log('Avatar set in localStorage:', localStorage.getItem("userAvatar"));
            
            window.location.href = "/index.html";
          } else {
            const error = await response.json();
            showError(error.message);
          }
        } catch (error) {
          console.error("Error during login:", error);
          showError("Connection error. Please try again.");
        }
      }

      function showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger alert-dismissible fade show';
        errorDiv.innerHTML = `
          ${message}
          <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
          </button>
        `;
        document.querySelector('.login-header').after(errorDiv);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
          $(errorDiv).alert('close');
        }, 5000);
      }

      function togglePassword() {
        const passwordInput = document.getElementById("login-password");
        const icon = document.querySelector(".toggle-password i");
        
        if (passwordInput.type === "password") {
          passwordInput.type = "text";
          icon.classList.remove("fa-eye");
          icon.classList.add("fa-eye-slash");
        } else {
          passwordInput.type = "password";
          icon.classList.remove("fa-eye-slash");
          icon.classList.add("fa-eye");
        }
      }
    </script>
  </body>
</html>
