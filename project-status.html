<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Project Status</title>
    <link rel="stylesheet" href="styles/style.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />
    <!-- Bootstrap CSS -->
    <link
      href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="styles/project-status.css">
    <!-- Add Chart.js DataLabels plugin -->
    
    <style>
      .pie-chart-container {
        height: 250px; /* Increased height to accommodate the summary table */
        position: relative;
        margin: 20px 0;
        display: flex;
        flex-direction: column;
      }
      
      .chart-summary-table {
        margin-top: 20px;
        text-align: center;
        font-size: 14px;
      }
      
      /* Level heading styles */
      .level-header {
        position: relative;
        width: 100%;
      }
      
      .level-header button.btn-primary {
        text-align: left;
        padding-right: 40px; /* Make room for the edit button */
      }
      
      .edit-level-heading-btn {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.8);
        padding: 0;
        margin: 0;
        z-index: 10;
      }
      
      .edit-level-heading-btn:hover {
        color: white;
      }
      
      .edit-level-heading-btn i {
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div class="container-fluid">
      <div class="row">
        <div class="main-content-project-status" id="main-content-project-status">
          <header class="top-bar-project-status">
            <button onclick="goHome()" class="home-button">
              <i class="fas fa-home"></i> Home
            </button>
            <div class="user-info">
              <div class="user-photo-container">
                <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp" alt="User Photo" class="user-photo" onerror="this.onerror=null;this.src='https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';" />
                <div class="user-dropdown-content">
                  <a href="/user-info.html" onclick="openSettings()"><i class="fas fa-cog"></i> Settings</a>
                </div>
              </div>
              <div class="user-details">
                <span class="user-name">Welcome, User</span>
                <span class="user-role">Role</span>
              </div>
              <button onclick="logout()" class="logout-button">Logout</button>
            </div>
          </header>

          <!-- Main content area -->
          <div class="content-wrapper">
            <!-- Left side: Project status table -->
            <div class="left-content">
              <div id="project-status-content" class="project-status-content">
                <h2>Project Status</h2>
                <!-- Project status tables will be dynamically added here -->
              </div>
            </div>

            <!-- Right side: Static content -->
            <div class="right-content-collapsible">
              <button id="toggle-summary-btn" class="toggle-summary-btn">➤</button>
              <div class="vertical-banner">Project Summary</div>
            <div class="right-content">
              <div class="summary-heading">
                <h3>Project Summary</h3>
                <span style="display: inline-block; width: 12px; height: 12px; background-color: #C0C0C0; margin-right: 5px;"></span> Yet to Start
                <span style="display: inline-block; width: 12px; height: 12px; background-color: #FFA500; margin-left: 15px; margin-right: 5px;"></span> In Progress
                <span style="display: inline-block; width: 12px; height: 12px; background-color: #008000; margin-left: 15px; margin-right: 5px;"></span> Completed
                <p class="mt-2 small text-muted">Values shown as weighted percentages</p>
              </div>

              <div class="overall-summary">
                <h4>Overall Project Progress</h4>
                <div class="pie-chart-container">
                  <canvas id="overallProjectPieChart"></canvas>
                </div>
              </div>

              <div class="chart-content">                
              </div>
            </div>
            </div>
              <!-- Pie charts will be dynamically added here -->
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Bootstrap JS and dependencies -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
      // Reusable Fetch Wrapper
      async function fetchWithAuth(url, options = {}) {
        const token = localStorage.getItem("token"); // Retrieve the token
        console.log("Token being sent:", token); // Debugging

        if (!token) {
          alert("Unauthorized access. Please log in.");
          window.location.href = "/login.html";
          throw new Error("No token found");
        }

        const headers = {
          ...options.headers,
          Authorization: `Bearer ${token}`,
        };

        const fetchOptions = {
          ...options,
          headers,
        };

        const response = await fetch(url, fetchOptions);

        if (response.status === 401 || response.status === 403) {
          alert("Session expired or unauthorized. Please log in again.");
          window.location.href = "/login.html";
          throw new Error("Unauthorized access");
        }

        return response;
      }

      document.addEventListener('DOMContentLoaded', () => {
        const token = localStorage.getItem('token');

        if (!token) {
            alert('Unauthorized access. Please login again.');
            window.location.href = '/login.html';
            return;
        }

        // Fetch projectId from URL
        const urlParams = new URLSearchParams(window.location.search);
        const projectId = urlParams.get('projectId');        
      });

      
      function getProjectIdFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const projectId = urlParams.get("projectId");
        console.log("Project ID from URL:", projectId); // Debugging
        return projectId;
      }

      document.addEventListener("DOMContentLoaded", async () => {
        const projectId = getProjectIdFromURL();
        if (!projectId) {
          console.error("Error: No project selected.");
          alert("Error: No project selected. Please try again.");
          return;
        }

        try {
          const response = await fetchWithAuth(`/api/project-status/${projectId}`);
          if (!response.ok) {
            throw new Error("Failed to fetch project status");
          }
          const statusData = await response.json();
          console.log("Project status data:", statusData);
          // Populate the table with the fetched status data
        } catch (error) {
          console.error("Error fetching project status:", error);
          alert("Error fetching project status. Please try again.");
        }
      });

      function goHome() {
  window.location.href = "index.html";
}

      function logout() {
            // Clear all user-related data
            const itemsToClear = ['token', 'username', 'role', 'userAvatar', 'userId'];
            itemsToClear.forEach(item => localStorage.removeItem(item));
            
            // Reset ALL user photos to default before redirecting
            const userPhotos = document.querySelectorAll('.user-photo, #userAvatar');
            userPhotos.forEach(photo => {
                photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
            });
            
            // Redirect to login page
            window.location.href = '/login.html';
        }

      function initializeCollapsibleLevels() {
        const projectId = getProjectIdFromURL();
        if (!projectId) return;
        
        // For each level container
        document.querySelectorAll('.table-container').forEach(container => {
            const levelButton = container.querySelector('button[data-toggle="collapse"]');
            const levelContent = container.querySelector('.collapse');
            
            if (!levelButton || !levelContent) return;
            
            // Extract level number from the data-target attribute
            const levelNumber = levelButton.getAttribute('data-target').replace('#level-', '');
            
            // Get saved state from localStorage
            const savedState = localStorage.getItem(`collapse-state-${projectId}-level-${levelNumber}`);
            
            // Apply saved state using Bootstrap's collapse method
            if (savedState === 'collapsed') {
                $(levelContent).collapse('hide');
            } else if (savedState === 'expanded' || savedState === null) {
                $(levelContent).collapse('show');
            }
            
            // Add click listener to save state
            $(levelContent).on('hidden.bs.collapse shown.bs.collapse', function(event) {
                const isCollapsed = event.type === 'hidden';
                localStorage.setItem(
                    `collapse-state-${projectId}-level-${levelNumber}`,
                    isCollapsed ? 'collapsed' : 'expanded'
                );
            });
        });
      }

      async function loadStatus() {
        const projectId = getProjectIdFromURL();
        if (!projectId) {
          console.error("Error: No project selected.");
          alert("Error: No project selected. Please try again.");
          return;
        }

        try {
          // Fetch project details
          const projectResponse = await fetchWithAuth(`/api/inprogressprojects/${projectId}`);
          if (!projectResponse.ok) {
            throw new Error("Failed to fetch project details");
          }
          const project = await projectResponse.json();
          
          // Update the header
          const header = document.querySelector("#project-status-content h2");
          if (header) {
            header.textContent = `Project Status - ${projectId}: ${project.title}`;
          }

          // Fetch project statuses
          const statusResponse = await fetchWithAuth(`/api/project-status/${projectId}`);
          if (!statusResponse.ok) {
            throw new Error("Failed to fetch project status");
          }

          const statusData = await statusResponse.json();
          const projectStatusContent = document.getElementById("project-status-content");

          if (!projectStatusContent) {
            throw new Error("Project status content element not found");
          }

          // Clear existing tables
          Array.from(projectStatusContent.querySelectorAll(".table-container"))
            .forEach(table => table.remove());

          // Create tables for each level
          statusData.forEach((levelData, index) => {
            const levelIdMatch = levelData.projectId ? levelData.projectId.match(/-level-(.+)$/) : null;
            const levelIdentifier = levelIdMatch ? levelIdMatch[1] : (index + 1);
            
            const tableContainer = document.createElement("div");
            tableContainer.className = "table-container";
            tableContainer.dataset.levelId = levelData.projectId || `${projectId}-level-${index + 1}`;

            const tableHtml = `
              <div class="level-header">
                <button class="btn btn-primary btn-block mb-1" type="button" 
                        data-toggle="collapse" data-target="#level-${levelIdentifier}">
                  <span id="level-heading-${levelIdentifier}">Level ${levelIdentifier}</span>
                  <button class="edit-level-heading-btn edit-control" 
                          onclick="editLevelHeading('${levelIdentifier}', event)">
                    <i class="fas fa-edit"></i>
                  </button>
                </button>
              </div>
              <div id="level-${levelIdentifier}" class="collapse show">
                <table id="status-table-level-${levelIdentifier}" class="table table-bordered">
                  <thead>
                    <tr>
                      ${levelData.headers.map((header, i) => `
                        <th>
                          <div class="d-flex align-items-center justify-content-between">
                            <span id="header-text-${levelIdentifier}-${i}">${header}</span>
                            <div>
                              ${i > 0 ? `
                                <button class="btn btn-sm btn-link edit-header-btn edit-control" 
                                        onclick="editHeader('${levelIdentifier}', ${i})">
                                  <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-link delete-column-btn edit-control" 
                                        onclick="deleteColumn('${levelIdentifier}', ${i})">
                                  <i class="fas fa-trash text-danger"></i>
                                </button>
                              ` : `
                                <button class="btn btn-sm btn-link edit-header-btn edit-control" 
                                        onclick="editHeader('${levelIdentifier}', ${i})">
                                  <i class="fas fa-edit"></i>
                                </button>
                              `}
                            </div>
                          </div>
                        </th>
                      `).join('')}
                    </tr>
                  </thead>
                  <tbody>
                    ${levelData.rows.map((row, rowIndex) => `
                      <tr>
                        ${row.map((cell, i) => {
                          if (i === 0) {
                            return `
                              <th>
                                <div class="d-flex align-items-center justify-content-between">
                                  <span id="row-label-${levelIdentifier}-${rowIndex}">${cell}</span>
                                  <div>
                                    <button class="btn btn-sm btn-link edit-row-label-btn edit-control" 
                                            onclick="editRowLabel('${levelIdentifier}', ${rowIndex})">
                                      <i class="fas fa-edit"></i>
                                    </button>
                                    ${rowIndex > 0 ? `
                                      <button class="btn btn-sm btn-link delete-row-btn edit-control" 
                                              onclick="deleteRow('${levelIdentifier}', ${rowIndex})">
                                        <i class="fas fa-trash text-danger"></i>
                                      </button>
                                    ` : ''}
                                  </div>
                                </div>
                              </th>
                            `;
                          } else {
                            return `
                              <td>
                                <select class="form-control status-dropdown" 
                                        onchange="handleStatusChange(this, '${levelIdentifier}', '${projectId}')"
                                        data-previous-value="${cell}">
                                  <option value="Yet to Start" ${cell === "Yet to Start" ? "selected" : ""}>
                                    Yet to Start
                                  </option>
                                  <option value="In Progress" ${cell === "In Progress" ? "selected" : ""}>
                                    In Progress
                                  </option>
                                  <option value="Completed" ${cell === "Completed" ? "selected" : ""}>
                                    Completed
                                  </option>
                                  <option value="NA" ${cell === "NA" ? "selected" : ""}>
                                    NA
                                  </option>
                                </select>
                              </td>
                            `;
                          }
                        }).join('')}
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
                <div class="table-actions">
                  <button class="btn btn-secondary toggle-edit-mode-btn" onclick="toggleEditMode('${levelIdentifier}')">
                    <i class="fas fa-edit"></i> Edit
                  </button>
                  <button class="btn btn-success add-row-btn edit-control" onclick="addRow('${levelIdentifier}')">
                    <i class="fas fa-plus"></i> Add Row
                  </button>
                  <button class="btn btn-warning add-column-btn edit-control" onclick="addColumn('${levelIdentifier}')">
                    <i class="fas fa-columns"></i> Add Column
                  </button>
                  <button class="btn btn-info wip-weightage-btn edit-control" onclick="openWIPWeightagePopup('${levelIdentifier}', '${projectId}', '${project.title}')">
                    <i class="fas fa-balance-scale"></i> WIP Weightage
                  </button>
                </div>
              </div>
            `;

            tableContainer.innerHTML = tableHtml;
            projectStatusContent.appendChild(tableContainer);

            // Initialize all dropdowns in this table
            const dropdowns = tableContainer.querySelectorAll('.status-dropdown');
            dropdowns.forEach(dropdown => {
              updateDropdownStyle(dropdown);
            });
          });

          // Initialize collapsible functionality after a short delay
          // to ensure Bootstrap has initialized all collapse elements
          setTimeout(() => {
            initializeCollapsibleLevels();
            initializeEditModeState(); // Make sure this is called
            hideTableActions(); // Add this line here
          }, 100);
        } catch (error) {
          console.error("Error loading project status:", error);
          alert("Error loading project status. Please try again.");
        }
      }

      function addRow(levelIdentifier) {
        const table = document.getElementById(`status-table-level-${levelIdentifier}`);
        const tbody = table.getElementsByTagName("tbody")[0];

        const rowName = prompt("Enter the name for the new row:");
        if (!rowName) {
            alert("Row name is required!");
            return;
        }

        const newRow = tbody.insertRow();
        const columnCount = table.getElementsByTagName("thead")[0].rows[0].cells.length;

        const headerCell = document.createElement('th');
        headerCell.textContent = rowName;
        newRow.appendChild(headerCell);

        for (let i = 1; i < columnCount; i++) {
            const cell = newRow.insertCell();
            const select = document.createElement('select');
            select.className = 'form-control status-dropdown';
            
            const options = ["Yet to Start", "In Progress", "Completed", "NA"];
            options.forEach(option => {
                const opt = document.createElement('option');
                opt.value = option;
                opt.textContent = option;
                select.appendChild(opt);
            });

            select.value = "Yet to Start";
            cell.appendChild(select);
        }

        // Save and refresh
        saveTable(levelIdentifier).then(() => {
            console.log("✅ Table saved successfully after adding new row");
            // Refresh the page while maintaining the current URL and scroll position
            const currentUrl = window.location.href;
            const scrollPos = window.scrollY;
            window.location.href = currentUrl + (currentUrl.includes('?') ? '&' : '?') + `scrollPos=${scrollPos}`;
        }).catch(error => {
            console.error("❌ Error saving table after adding row:", error);
            tbody.removeChild(newRow);
            alert(`Failed to save new row: ${error.message}`);
        });
      }

      function addColumn(levelIdentifier) {
        const table = document.getElementById(`status-table-level-${levelIdentifier}`);
        const headerRow = table.getElementsByTagName("thead")[0].rows[0];
        
        const columnHeader = prompt("Enter header for the new column:");
        if (!columnHeader) {
            alert("Column header is required!");
            return;
        }

        // Add header cell
        const th = document.createElement("th");
        const headerIndex = headerRow.cells.length;
        
        // Create header text span with ID
        const headerText = document.createElement("span");
        headerText.id = `header-text-${levelIdentifier}-${headerIndex}`;
        headerText.textContent = columnHeader;
        th.appendChild(headerText);

        // Add edit button if needed
        const editButton = document.createElement("button");
        editButton.className = "edit-header-btn";
        editButton.textContent = "✎";
        editButton.onclick = () => editHeader(levelIdentifier, headerIndex);
        th.appendChild(editButton);

        headerRow.appendChild(th);

        // Add cells to all rows
        const tbody = table.getElementsByTagName("tbody")[0];
        for (const row of tbody.rows) {
            const td = document.createElement("td");
            const select = document.createElement("select");
            
            const options = ["Yet to Start", "In Progress", "Complete", "N/A"];
            options.forEach(option => {
                const opt = document.createElement("option");
                opt.value = option;
                opt.textContent = option;
                select.appendChild(opt);
            });
            
            td.appendChild(select);
            row.appendChild(td);
        }

        // Save and refresh
        saveTable(levelIdentifier).then(() => {
            console.log("✅ Table saved successfully after adding new column");
            // Refresh the page while maintaining the current URL and scroll position
            const currentUrl = window.location.href;
            const scrollPos = window.scrollY;
            window.location.href = currentUrl + (currentUrl.includes('?') ? '&' : '?') + `scrollPos=${scrollPos}`;
        }).catch(error => {
            console.error("❌ Error saving table after adding column:", error);
            alert(`Failed to save new column: ${error.message}`);
        });
      }

      async function saveTable(levelIdentifier) {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            const projectId = urlParams.get('projectId');
            
            if (!projectId) {
                throw new Error('Project ID not found in URL');
            }

            const table = document.getElementById(`status-table-level-${levelIdentifier}`);
            if (!table) {
                throw new Error('Table not found');
            }

            // Get headers
            const headers = Array.from(table.querySelectorAll('thead th')).map(th => {
                const headerSpan = th.querySelector(`[id^="header-text-${levelIdentifier}"]`);
                return headerSpan ? headerSpan.textContent : '';
            }).filter(header => header !== '');

            // Get rows
            const rows = Array.from(table.querySelectorAll('tbody tr')).map(tr => {
                const firstCell = tr.querySelector('th').textContent;
                const otherCells = Array.from(tr.querySelectorAll('td select')).map(select => select.value);
                return [firstCell, ...otherCells];
            });

            console.log("🔍 Saving table data:", {
                projectId,
                level: levelIdentifier,
                headers,
                rows
            });

            const response = await fetchWithAuth('/api/project-status/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    projectId,
                    level: levelIdentifier,
                    headers,
                    rows
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to save table data');
            }

            const result = await response.json();
            console.log("✅ Save Response:", result);
            return result; // Return the result instead of showing alert

        } catch (error) {
            console.error("❌ Error saving table:", error);
            throw error; // Re-throw the error for handling by the caller
        }
      }


      function hideTableActions() {
    const userRole = localStorage.getItem('role');
    console.log("UserRole from localStorage:", userRole);

    if (userRole !== 'Admin' && userRole !== 'Project Manager') {
        console.log("Hiding edit controls for non-privileged users");
        
        // Hide the edit toggle buttons for non-privileged users
        const editButtons = document.querySelectorAll('.toggle-edit-mode-btn');
        editButtons.forEach(button => {
            button.classList.add('d-none');
        });
        
        // No need to hide WIP weightage buttons separately since they're now edit controls
        // and will be hidden by default when not in edit mode
    }
}

      function updateDropdownStyle(dropdown) {
        if (!dropdown) return;

        const colors = {
            "Yet to Start": { bg: "#C0C0C0", text: "black" },
            "In Progress": { bg: "#FFA500", text: "white" },
            "Completed": { bg: "#008000", text: "white" }
        };

        const style = colors[dropdown.value] || { bg: "", text: "black" };
        
        dropdown.style.backgroundColor = style.bg;
        dropdown.style.color = style.text;
      }

      async function handleStatusChange(dropdown, levelIndex, projectId) {
        if (!dropdown) return;
        
        const newValue = dropdown.value;
        const previousValue = dropdown.getAttribute('data-previous-value');
        
        try {
          const tableContainer = dropdown.closest('.table-container');
          const levelId = tableContainer.dataset.levelId;
          
          // Extract the level identifier from the levelId
          const levelIdMatch = levelId ? levelId.match(/-level-(.+)$/) : null;
          const levelIdentifier = levelIdMatch ? levelIdMatch[1] : levelIndex;
          
          const cell = dropdown.closest('td');
          const row = cell.closest('tr');
          const rowIndex = Array.from(row.parentElement.children).indexOf(row);
          const columnIndex = Array.from(row.children).indexOf(cell);

          const response = await fetchWithAuth('/api/project-status/update', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              projectId,
              level: levelIdentifier,
              rowIndex,
              columnIndex,
              newStatus: newValue
            })
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Failed to update status');
          }

          const result = await response.json();
          console.log("✅ Status updated successfully:", result);
          
          dropdown.setAttribute('data-previous-value', newValue);
          updateDropdownStyle(dropdown);
          
        } catch (error) {
          console.error("❌ Error updating status:", error);
          alert(`Failed to update status: ${error.message}`);
          
          // Revert to previous value on error
          dropdown.value = previousValue;
          updateDropdownStyle(dropdown);
        }
      }

      document.addEventListener("DOMContentLoaded", loadStatus);

      function editHeader(levelIndex, headerIndex) {
        const headerText = document.getElementById(`header-text-${levelIndex}-${headerIndex}`);
        const newHeader = prompt("Edit header:", headerText.textContent);
        
        if (newHeader && newHeader.trim()) {
            headerText.textContent = newHeader.trim();
            saveTable(levelIndex);
        }
      }

      async function saveUpdatedHeader(levelIndex, headerIndex, newText) {
        const projectId = getProjectIdFromURL();
        const formattedProjectId = `${projectId}-level-${levelIndex}`;

        const updateData = {
          projectId: formattedProjectId,
          headerIndex,
          newText,
        };

        try {
          const response = await fetch(`/api/project-status/update-header`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(updateData),
          });

          if (response.ok) {
            console.log(`✅ Header updated successfully for Level ${levelIndex}, Header ${headerIndex}`);
          } else {
            console.error("❌ Failed to update header:", await response.text());
          }
        } catch (error) {
          console.error("Error updating header:", error);
        }
      }

      async function openWIPWeightagePopup(levelIdentifier, projectId, projectTitle) {
        const table = document.getElementById(`status-table-level-${levelIdentifier}`);
        const headers = Array.from(table.getElementsByTagName("thead")[0].rows[0].cells).map(
          (cell) => cell.textContent
        );
      
        const modalTitle = document.getElementById("wipWeightageModalTitle");
        modalTitle.textContent = `WIP Weightage - ${projectId}: ${levelIdentifier}`;
      
        const formattedProjectId = `${projectId}-level-${levelIdentifier}`;
        let existingWeightages = [];
        try {
          const response = await fetchWithAuth(`/api/project-wip-weightage/${formattedProjectId}`);
          if (response.ok) {
            const data = await response.json();
            existingWeightages = data.weightages || [];
          } else {
            console.warn("⚠️ No existing weightages found for this level.");
          }
        } catch (error) {
          console.error("❌ Error fetching existing WIP Weightage:", error);
        }
      
        // Populate the form with input fields for each column
        const wipWeightageInputs = document.getElementById("wipWeightageInputs");
        wipWeightageInputs.innerHTML = ""; // Clear previous inputs
        headers.forEach((header, index) => {
          if (index > 0) {
            const existingValue = existingWeightages[index - 1] || "";
            wipWeightageInputs.innerHTML += `
              <div class="form-group">
                <label for="weightage-${index}">${header}</label>
                <input type="number" class="form-control wip-weightage-input" id="weightage-${index}" min="0" max="100" step="1" placeholder="Enter percentage" value="${existingValue}" />
              </div>
            `;
          }
        });
      
        const modal = document.getElementById("wipWeightageModal");
        modal.style.display = "block";
      }
      
      function closeWIPWeightagePopup() {
        const modal = document.getElementById("wipWeightageModal");
        modal.style.display = "none";
      }
    
      async function saveWIPWeightage() {
        const inputs = document.querySelectorAll(".wip-weightage-input");
        const weightages = [];
        let totalWeightage = 0;
      
        inputs.forEach((input) => {
          const value = parseFloat(input.value) || 0;
          totalWeightage += value;
          weightages.push(value);
        });
      
        if (totalWeightage != 100) {
          alert("Total weightage is not 100%");
          return;
        }
      
        // Extract projectId and levelIndex from the modal title
        const modalTitle = document.getElementById("wipWeightageModalTitle").textContent;
        const [projectId, levelIndex] = modalTitle.split(" - ")[1].split(":").map((item) => item.trim());
      
        const formattedProjectId = `${projectId}-level-${levelIndex}`;
      
        const wipData = {
          projectId: formattedProjectId,
          level: levelIndex,
          weightages,
        };
      
        console.log("🚀 Sending WIP Weightage Data to Backend:", wipData); // Debugging
      
        const token = localStorage.getItem("token"); // Retrieve the token
        console.log("Token being sent:", token); // Debugging
      
        try {
          const response = await fetch(`/api/project-wip-weightage/save`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`, // Include the token in the Authorization header
            },
            body: JSON.stringify(wipData),
          });
      
          if (response.ok) {
            alert("✅ WIP Weightage saved successfully!");
            closeWIPWeightagePopup();
          } else {
            const errorText = await response.text();
            console.error("❌ Backend Error:", errorText);
            alert("❌ Failed to save WIP Weightage.");
          }
        } catch (error) {
          console.error("Error saving WIP Weightage:", error);
          alert("Error saving WIP Weightage. Please try again.");
        }
      }

async function loadOverallProjectPieChart() {
  const projectId = getProjectIdFromURL();
  if (!projectId) {
    console.error("Error: No project selected!");
    return;
  }

  try {
    // Fetch the cumulative WIP data for this project
    const response = await fetchWithAuth(`/api/project-cumulative-wip/${projectId}`);
    if (!response.ok) {
      throw new Error("Failed to fetch cumulative WIP data");
    }

    const data = await response.json();
    console.log("Cumulative WIP Data:", data);

    // Extract the data from the correct structure
    // The data is nested under cumulativeWIP property
    const cumulativeData = data.cumulativeWIP || data;
    
    const yetToStart = cumulativeData.yetToStart || 0;
    const inProgress = cumulativeData.inProgress || 0;
    const completed = cumulativeData.completed || 0;

    console.log("Extracted values for pie chart:", {
      yetToStart, inProgress, completed
    });

    // Render the pie chart
    renderOverallPieChart(yetToStart, inProgress, completed);
  } catch (error) {
    console.error("Error fetching cumulative WIP data:", error);
    // Render an empty pie chart or error message
    renderOverallPieChart(0, 0, 0);
  }
}

function renderOverallPieChart(yetToStart, inProgress, completed) {
  const ctx = document.getElementById('overallProjectPieChart').getContext('2d');
  
  // First, clear any existing chart and summary
  const chartContainer = document.querySelector('.pie-chart-container');
  
  // Remove ALL existing summary tables (there might be multiple due to the bug)
  const existingSummaries = chartContainer.querySelectorAll('.chart-summary-table');
  existingSummaries.forEach(summary => summary.remove());
  
  // Destroy any existing chart to prevent duplicates
  Chart.getChart('overallProjectPieChart')?.destroy();
  
  // Calculate percentages
  const total = yetToStart + inProgress + completed;
  const yetToStartPct = total > 0 ? (yetToStart / total * 100).toFixed(1) : 0;
  const inProgressPct = total > 0 ? (inProgress / total * 100).toFixed(1) : 0;
  const completedPct = total > 0 ? (completed / total * 100).toFixed(1) : 0;
  
  // Create the pie chart
  const chart = new Chart(ctx, {
    type: 'pie',
    data: {
      labels: [
        `Yet to Start: ${yetToStartPct}%`, 
        `In Progress: ${inProgressPct}%`, 
        `Completed: ${completedPct}%`
      ],
      datasets: [{
        data: [yetToStart, inProgress, completed],
        backgroundColor: [
          '#C0C0C0', // Gray for "Yet to Start"
          '#FFA500', // Orange for "In Progress"
          '#008000', // Green for "Completed"
        ],
        borderColor: [
          '#C0C0C0',
          '#FFA500',
          '#008000',
        ],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'bottom',
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              size: 12
            },
            color: function(context) {
              // Match the legend text color to the segment color
              return context.chart.data.datasets[0].backgroundColor[context.dataIndex];
            },
            generateLabels: function(chart) {
              // Custom label generator to include percentages
              const data = chart.data;
              if (data.labels.length && data.datasets.length) {
                return data.labels.map(function(label, i) {
                  const meta = chart.getDatasetMeta(0);
                  const style = meta.controller.getStyle(i);
                  
                  return {
                    text: label,
                    fillStyle: style.backgroundColor,
                    strokeStyle: style.borderColor,
                    lineWidth: style.borderWidth,
                    hidden: !chart.getDataVisibility(i),
                    index: i
                  };
                });
              }
              return [];
            }
          }
        },
        tooltip: {
          enabled: true,
          callbacks: {
            label: function(context) {
              const label = context.label || '';
              return label; // Label already includes percentage
            }
          }
        }
      }
    }
  });
  
  return chart;
}

// Call this function when the page loads
document.addEventListener('DOMContentLoaded', function() {
  loadOverallProjectPieChart();
});

      async function loadProjectSummary() {
        const projectId = getProjectIdFromURL();
        if (!projectId) {
          console.error("Error: No project selected!");
          alert("Error: No project selected. Please try again.");
          return;
        }

        try {
          const response = await fetchWithAuth(`/api/project-wip/${projectId}`);
          console.log("API Response Status:", response.status); // Debugging

          if (!response.ok) {
            throw new Error("Failed to fetch WIP data");
          }

          const projectWIPData = await response.json();
          console.log("WIP Data:", projectWIPData); // Debugging

          // Clear existing charts
          document.querySelector(".chart-content").innerHTML = "";
          
          // Create a container for the combined chart
          const chartContainer = document.createElement("div");
          chartContainer.className = "chart-container combined-chart";
          chartContainer.innerHTML = `
            <h4>All Levels Progress</h4>
            <canvas id="combined-levels-chart"></canvas>
          `;
          document.querySelector(".chart-content").appendChild(chartContainer);
          
          // Prepare data for combined chart
          const labels = [];
          const yetToStartData = [];
          const inProgressData = [];
          const completedData = [];
          
          // Loop through each level and collect data
          projectWIPData.forEach((levelData, index) => {
            const columnCounts = levelData.columnCounts;
            
            // Extract level identifier from projectId
            const levelIdMatch = levelData.projectId ? levelData.projectId.match(/-level-(.+)$/) : null;
            const levelIdentifier = levelIdMatch ? levelIdMatch[1] : (index + 1);
            
            // Add level to labels
            labels.push(`Level ${levelIdentifier}`);
            
            // Aggregate data for the chart
            let yetToStart = 0;
            let inProgress = 0;
            let completed = 0;
            
            Object.values(columnCounts).forEach((column) => {
              yetToStart += column["Weighted Ratio (Yet to Start %)"] || 0;
              inProgress += column["Weighted Ratio (In Progress %)"] || 0;
              completed += column["Weighted Ratio (Completed %)"] || 0;
            });
            
            // Add data to respective arrays
            yetToStartData.push(yetToStart);
            inProgressData.push(inProgress);
            completedData.push(completed);
            
            console.log(`Data for Level ${levelIdentifier}:`, { yetToStart, inProgress, completed });
          });
          
          // Create the combined stacked bar chart
          renderCombinedBarChart(labels, yetToStartData, inProgressData, completedData);
          
        } catch (error) {
          console.error("Error fetching WIP data:", error);
          alert("Error fetching WIP data. Please try again.");
        }
      }

      function renderCombinedBarChart(labels, yetToStartData, inProgressData, completedData) {
        const ctx = document.getElementById('combined-levels-chart').getContext('2d');
        
        // Destroy any existing chart to prevent duplicates
        Chart.getChart('combined-levels-chart')?.destroy();
        
        const chartConfig = {
          type: 'bar',
          data: {
            labels: labels,
            datasets: [
              // Order matters for stacking - first dataset goes on bottom
              {
                label: 'Completed',
                data: completedData,
                backgroundColor: '#008000', // Green
                borderColor: '#008000',
                borderWidth: 1
              },
              {
                label: 'In Progress',
                data: inProgressData,
                backgroundColor: '#FFA500', // Orange
                borderColor: '#FFA500',
                borderWidth: 1
              },
              {
                label: 'Yet to Start',
                data: yetToStartData,
                backgroundColor: '#C0C0C0', // Gray
                borderColor: '#C0C0C0',
                borderWidth: 1
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: true,
            aspectRatio: 2, // Wider aspect ratio for multiple bars
            scales: {
              x: {
                stacked: true,
                grid: {
                  display: false
                }
              },
              y: {
                stacked: true,
                beginAtZero: true,
                max: 110, // Increased to make room for labels
                ticks: {
                  callback: function(value) {
                    return value <= 100 ? value + '%' : '';
                  }
                },
                grid: {
                  display: true,
                  color: 'rgba(0, 0, 0, 0.1)'
                }
              }
            },
            plugins: {
              legend: {
                position: 'top',
                reverse: true
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    return context.dataset.label + ': ' + context.parsed.y.toFixed(1) + '%';
                  }
                }
              }
            }
          },
          plugins: [{
            id: 'barLabels',
            afterDatasetsDraw: function(chart) {
              const ctx = chart.ctx;
              ctx.font = 'bold 11px Arial';
              ctx.textAlign = 'center';
              
              // For each x-axis label (each stacked bar)
              chart.data.labels.forEach((label, barIndex) => {
                // Get values for each segment
                const yetToStartValue = yetToStartData[barIndex];
                const inProgressValue = inProgressData[barIndex];
                const completedValue = completedData[barIndex];
                
                // Only show labels for segments with significant values (> 5%)
                const minValueToShow = 5;
                
                // Get positions for each segment
                const yetToStartMeta = chart.getDatasetMeta(2); // Yet to Start (top)
                const inProgressMeta = chart.getDatasetMeta(1); // In Progress (middle)
                const completedMeta = chart.getDatasetMeta(0); // Completed (bottom)
                
                const barX = yetToStartMeta.data[barIndex].x; // X position is the same for all segments
                
                // Calculate Y positions for center of each segment
                // For Yet to Start (top segment)
                if (yetToStartValue > minValueToShow) {
                  const yetToStartY = yetToStartMeta.data[barIndex].y + 
                                     (yetToStartMeta.data[barIndex].height / 2);
                  
                  // White text on gray background
                  ctx.fillStyle = '#FFFFFF';
                  ctx.fillText(yetToStartValue.toFixed(1) + '%', barX, yetToStartY);
                }
                
                // For In Progress (middle segment)
                if (inProgressValue > minValueToShow) {
                  // Calculate the center Y position of the In Progress segment
                  const inProgressY = inProgressMeta.data[barIndex].y + 
                                     (inProgressMeta.data[barIndex].height / 2);
                  
                  // White text on orange background
                  ctx.fillStyle = '#FFFFFF';
                  ctx.fillText(inProgressValue.toFixed(1) + '%', barX, inProgressY);
                }
                
                // For Completed (bottom segment)
                if (completedValue > minValueToShow) {
                  // Calculate the center Y position of the Completed segment
                  const completedY = completedMeta.data[barIndex].y + 
                                    (completedMeta.data[barIndex].height / 2);
                  
                  // White text on green background
                  ctx.fillStyle = '#FFFFFF';
                  ctx.fillText(completedValue.toFixed(1) + '%', barX, completedY);
                }        
                
              });
            }
          }]
        };
        
        new Chart(ctx, chartConfig);
      }
      
      // Load project summary on page load
      document.addEventListener("DOMContentLoaded", loadProjectSummary);

      document.addEventListener("DOMContentLoaded", () => {
        const toggleButton = document.getElementById("toggle-summary-btn");
        const rightContent = document.querySelector(".right-content");
        const verticalBanner = document.querySelector(".vertical-banner");

        toggleButton.addEventListener("click", () => {
          rightContent.classList.toggle("collapsed");
          toggleButton.classList.toggle("collapsed");

          // Update button text or icon
          if (rightContent.classList.contains("collapsed")) {
            toggleButton.textContent = "◀"; // Collapsed state
            verticalBanner.style.opacity = "1"; // Show vertical banner
          } else {
            toggleButton.textContent = "➤"; // Expanded state
            verticalBanner.style.opacity = "0"; // Hide vertical banner
          }
        });
      });

      document.addEventListener("DOMContentLoaded", () => {
        const userNameElement = document.querySelector(".user-name");
        const userRoleElement = document.querySelector(".user-role");

        // Retrieve the user's role and name from localStorage
        const userRole = localStorage.getItem("role");
        const userName = localStorage.getItem("username"); // Retrieve the username from localStorage

        // Update the user profile section
        if (userNameElement) {
          userNameElement.textContent = `Welcome, ${userName || "User"}`;
        }
        if (userRoleElement) {
          userRoleElement.textContent = userRole || "Role not found";
        }

        console.log("User Role:", userRole); // Debugging
        console.log("User Name:", userName); // Debugging
      });

      async function logout() {
        try {
          const response = await fetch("/api/logout", {
            method: "POST",
          });
          if (response.ok) {
            window.location.href = "/login.html";
          } else {
            console.error("Logout failed");
          }
        } catch (error) {
          console.error("Error logging out:", error);
        }
      } 
      
      // Make sure this function is defined in the global scope
      function editLevelHeading(levelNumber, event) {
        // Stop the event from propagating to the parent button
        event.stopPropagation();
        
        // Find the table container that contains this button
        const button = event.target.closest('.edit-level-heading-btn');
        if (!button) return;
        
        const levelHeader = button.closest('.level-header');
        if (!levelHeader) return;
        
        const tableContainer = levelHeader.closest('.table-container');
        if (!tableContainer) return;
        
        // Get the actual level ID from the data attribute
        const levelId = tableContainer.dataset.levelId;
        if (!levelId) {
          console.error("Level ID not found in table container");
          return;
        }
        
        // Extract the level identifier from the levelId
        const levelIdMatch = levelId.match(/-level-(.+)$/);
        if (!levelIdMatch) {
          console.error(`Invalid level ID format: ${levelId}`);
          return;
        }
        
        const actualLevelNumber = levelIdMatch[1];
        
        // Find the heading element within this level header
        const headingElement = levelHeader.querySelector(`[id^="level-heading-"]`);
        if (!headingElement) {
          console.error("Heading element not found");
          return;
        }
        
        const currentHeading = headingElement.textContent;
        console.log(`Editing level ${actualLevelNumber} heading: "${currentHeading}"`);
        
        const newHeading = prompt("Edit level heading:", currentHeading);
        
        if (newHeading && newHeading.trim()) {
          headingElement.textContent = newHeading.trim();
          
          // Save the updated heading to the server
          saveLevelHeading(levelId, newHeading.trim());
        }
      }

      // Update the function to save level headings to the server
      async function saveLevelHeading(levelId, newHeading) {
        const projectId = getProjectIdFromURL();
        if (!projectId) {
          alert("Error: No project ID found");
          return;
        }
        
        try {
          // Extract the level number from the levelId
          const levelIdMatch = levelId.match(/-level-(.+)$/);
          if (!levelIdMatch) {
            throw new Error(`Invalid level ID format: ${levelId}`);
          }
          
          const levelNumber = levelIdMatch[1];
          console.log(`Updating level ${levelNumber} heading to "${newHeading}" for project ${projectId}`);
          console.log(`Full level ID: ${levelId}`);
          
          const requestData = {
            projectId,
            levelNumber,
            newHeading
          };
          
          console.log("Sending request data:", requestData);
          
          const response = await fetchWithAuth('/api/project-status/update-level-heading', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
          });
          
          const responseText = await response.text();
          console.log(`Response status: ${response.status}, text: ${responseText}`);
          
          if (!response.ok) {
            let errorMessage = 'Failed to update level heading';
            try {
              const errorData = JSON.parse(responseText);
              errorMessage = errorData.message || errorMessage;
              if (errorData.error) {
                errorMessage += `: ${errorData.error}`;
              }
            } catch (e) {
              // If parsing JSON fails, use the response text
              errorMessage = `${errorMessage}: ${responseText || response.statusText}`;
            }
            throw new Error(errorMessage);
          }
          
          try {
            const result = JSON.parse(responseText);
            console.log(`✅ Level heading updated successfully:`, result);
            
            // Reload the page to reflect the updated projectId
            window.location.reload();
          } catch (e) {
            console.error("Error parsing JSON response:", e);
            // Still reload if we got a 200 OK
            window.location.reload();
          }
        } catch (error) {
          console.error('❌ Error updating level heading:', error);
          alert(`Failed to save level heading: ${error.message}`);
        }
      }

      // Function to delete a row
      async function deleteRow(levelIndex, rowIndex) {
        // Confirm deletion
        if (!confirm("Are you sure you want to delete this row? This action cannot be undone.")) {
          return;
        }
        
        try {
          const table = document.getElementById(`status-table-level-${levelIndex}`);
          if (!table) {
            throw new Error("Table not found");
          }
          
          const tbody = table.querySelector('tbody');
          if (!tbody) {
            throw new Error("Table body not found");
          }
          
          // Get the row to delete
          const rows = tbody.querySelectorAll('tr');
          if (rowIndex >= rows.length) {
            throw new Error("Row index out of bounds");
          }
          
          // Remove the row from the DOM
          tbody.removeChild(rows[rowIndex]);
          
          // Save the updated table
          await saveTable(levelIndex);
          
          console.log(`✅ Row ${rowIndex} deleted successfully from level ${levelIndex}`);
        } catch (error) {
          console.error(`❌ Error deleting row:`, error);
          alert(`Failed to delete row: ${error.message}`);
        }
      }

      // Function to delete a column
      async function deleteColumn(levelIndex, columnIndex) {
        // Confirm deletion
        if (!confirm("Are you sure you want to delete this column? This action cannot be undone.")) {
          return;
        }
        
        try {
          const table = document.getElementById(`status-table-level-${levelIndex}`);
          if (!table) {
            throw new Error("Table not found");
          }
          
          // Get all rows (including header row)
          const headerRow = table.querySelector('thead tr');
          const bodyRows = table.querySelectorAll('tbody tr');
          
          if (!headerRow) {
            throw new Error("Header row not found");
          }
          
          // Remove the column header
          const headerCells = headerRow.querySelectorAll('th');
          if (columnIndex >= headerCells.length) {
            throw new Error("Column index out of bounds");
          }
          
          headerRow.removeChild(headerCells[columnIndex]);
          
          // Remove the column from each body row
          bodyRows.forEach(row => {
            const cells = row.querySelectorAll('th, td');
            if (columnIndex < cells.length) {
              row.removeChild(cells[columnIndex]);
            }
          });
          
          // Save the updated table
          await saveTable(levelIndex);
          
          console.log(`✅ Column ${columnIndex} deleted successfully from level ${levelIndex}`);
        } catch (error) {
          console.error(`❌ Error deleting column:`, error);
          alert(`Failed to delete column: ${error.message}`);
        }
      }

      // Function to edit row labels
      function editRowLabel(levelIndex, rowIndex) {
        const labelElement = document.getElementById(`row-label-${levelIndex}-${rowIndex}`);
        if (!labelElement) {
          console.error(`Row label element not found for level ${levelIndex}, row ${rowIndex}`);
          return;
        }
        
        const currentLabel = labelElement.textContent;
        console.log(`Editing row label for level ${levelIndex}, row ${rowIndex}: "${currentLabel}"`);
        
        const newLabel = prompt("Edit row label:", currentLabel);
        
        if (newLabel && newLabel.trim()) {
          labelElement.textContent = newLabel.trim();
          
          // Save the table to persist the changes
          saveTable(levelIndex).then(() => {
            console.log(`✅ Row label updated successfully for level ${levelIndex}, row ${rowIndex}`);
          }).catch(error => {
            console.error(`❌ Error saving table after updating row label:`, error);
            // Revert the change if saving fails
            labelElement.textContent = currentLabel;
            alert(`Failed to save row label: ${error.message}`);
          });
        }
      }

      // Add this function to toggle edit mode
      function toggleEditMode(levelIdentifier) {
        const tableContainer = document.querySelector(`.table-container[data-level-id$="-level-${levelIdentifier}"]`);
        if (!tableContainer) {
          console.error(`Table container not found for level ${levelIdentifier}`);
          return;
        }
        
        // Toggle edit mode class on the table container
        tableContainer.classList.toggle('edit-mode');
        
        // Get the edit/save button
        const editButton = tableContainer.querySelector('.toggle-edit-mode-btn');
        if (!editButton) {
          console.error(`Edit button not found for level ${levelIdentifier}`);
          return;
        }
        
        // Update button text and icon based on current mode
        const isEditMode = tableContainer.classList.contains('edit-mode');
        
        if (isEditMode) {
          // Switch to Save mode
          editButton.innerHTML = '<i class="fas fa-save"></i> Save';
          editButton.classList.remove('btn-secondary');
          editButton.classList.add('btn-primary');
        } else {
          // Switch to Edit mode
          editButton.innerHTML = '<i class="fas fa-edit"></i> Edit';
          editButton.classList.remove('btn-primary');
          editButton.classList.add('btn-secondary');
          
          // Save the table when exiting edit mode
          saveTable(levelIdentifier).then(() => {
            console.log(`✅ Table saved successfully after exiting edit mode`);
          }).catch(error => {
            console.error(`❌ Error saving table:`, error);
            alert(`Failed to save table: ${error.message}`);
          });
        }
      }

      // Update the table HTML generation to include the edit/save toggle button
      // and add the table-container data-level-id attribute
      const tableContainer = document.createElement('div');
      tableContainer.className = 'table-container';
      tableContainer.dataset.levelId = `${projectId}-level-${levelIdentifier}`;

      const tableHtml = `
        <div class="level-header">
          <button class="btn btn-primary btn-block mb-1" type="button" 
                  data-toggle="collapse" data-target="#level-${levelIdentifier}">
            <span id="level-heading-${levelIdentifier}">Level ${levelIdentifier}</span>
            <button class="edit-level-heading-btn edit-control" 
                    onclick="editLevelHeading('${levelIdentifier}', event)">
              <i class="fas fa-edit"></i>
            </button>
          </button>
        </div>
        <div id="level-${levelIdentifier}" class="collapse show">
          <table id="status-table-level-${levelIdentifier}" class="table table-bordered">
            <thead>
              <tr>
                ${levelData.headers.map((header, i) => `
                  <th>
                    <div class="d-flex align-items-center justify-content-between">
                      <span id="header-text-${levelIdentifier}-${i}">${header}</span>
                      <div>
                        ${i > 0 ? `
                          <button class="btn btn-sm btn-link edit-header-btn edit-control" 
                                  onclick="editHeader('${levelIdentifier}', ${i})">
                            <i class="fas fa-edit"></i>
                          </button>
                          <button class="btn btn-sm btn-link delete-column-btn edit-control" 
                                  onclick="deleteColumn('${levelIdentifier}', ${i})">
                            <i class="fas fa-trash text-danger"></i>
                          </button>
                        ` : `
                          <button class="btn btn-sm btn-link edit-header-btn edit-control" 
                                  onclick="editHeader('${levelIdentifier}', ${i})">
                            <i class="fas fa-edit"></i>
                          </button>
                        `}
                      </div>
                    </div>
                  </th>
                `).join('')}
              </tr>
            </thead>
            <tbody>
              ${levelData.rows.map((row, rowIndex) => `
                <tr>
                  ${row.map((cell, i) => {
                    if (i === 0) {
                      return `
                        <th>
                          <div class="d-flex align-items-center justify-content-between">
                            <span id="row-label-${levelIdentifier}-${rowIndex}">${cell}</span>
                            <div>
                              <button class="btn btn-sm btn-link edit-row-label-btn edit-control" 
                                      onclick="editRowLabel('${levelIdentifier}', ${rowIndex})">
                                <i class="fas fa-edit"></i>
                              </button>
                              ${rowIndex > 0 ? `
                                <button class="btn btn-sm btn-link delete-row-btn edit-control" 
                                        onclick="deleteRow('${levelIdentifier}', ${rowIndex})">
                                  <i class="fas fa-trash text-danger"></i>
                                </button>
                              ` : ''}
                            </div>
                          </div>
                        </th>
                      `;
                    } else {
                      return `
                        <td>
                          <select class="form-control status-dropdown" 
                                  onchange="handleStatusChange(this, '${levelIdentifier}', '${projectId}')"
                                  data-previous-value="${cell}">
                            <option value="Yet to Start" ${cell === "Yet to Start" ? "selected" : ""}>
                              Yet to Start
                            </option>
                            <option value="In Progress" ${cell === "In Progress" ? "selected" : ""}>
                              In Progress
                            </option>
                            <option value="Completed" ${cell === "Completed" ? "selected" : ""}>
                              Completed
                            </option>
                            <option value="NA" ${cell === "NA" ? "selected" : ""}>
                              NA
                            </option>
                          </select>
                        </td>
                      `;
                    }
                  }).join('')}
                </tr>
              `).join('')}
            </tbody>
          </table>
          <div class="table-actions">
            <button class="btn btn-secondary toggle-edit-mode-btn" onclick="toggleEditMode('${levelIdentifier}')">
              <i class="fas fa-edit"></i> Edit
            </button>
            <button class="btn btn-success add-row-btn edit-control" onclick="addRow('${levelIdentifier}')">
              <i class="fas fa-plus"></i> Add Row
            </button>
            <button class="btn btn-warning add-column-btn edit-control" onclick="addColumn('${levelIdentifier}')">
              <i class="fas fa-columns"></i> Add Column
            </button>
            <button class="btn btn-info wip-weightage-btn edit-control" onclick="openWIPWeightagePopup('${levelIdentifier}', '${projectId}', '${project.title}')">
              <i class="fas fa-balance-scale"></i> WIP Weightage
            </button>
          </div>
        </div>
      `;

      tableContainer.innerHTML = tableHtml;
      projectStatusContent.appendChild(tableContainer);
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const token = localStorage.getItem("token"); // Retrieve the token
        if (!token) {
          alert("Unauthorized access. Please log in.");
          window.location.href = "/login.html";
        }
      });
    </script>
    <div id="wipWeightageModal" class="modal" tabindex="-1" role="dialog">
      <div class="modal-dialog" role="document">
          <div class="modal-content">
              <div class="modal-header">
                  <h5 class="modal-title" id="wipWeightageModalTitle">WIP Weightage</h5>
                  <button type="button" class="close" onclick="closeWIPWeightagePopup()" aria-label="Close">
                      <span aria-hidden="true">&times;</span>
                  </button>
              </div>
              <div class="modal-body">
                  <form id="wipWeightageForm">
                      <div id="wipWeightageInputs"></div>
                      <div class="form-group">
                          <button type="button" class="btn btn-primary" onclick="saveWIPWeightage()">Save</button>
                      </div>
                  </form>
              </div>
          </div>
      </div>
    </div>
    <script>
      // Add this function to update user info including avatar
      function updateUserInterface() {
          const userRole = localStorage.getItem('role');
          const userName = localStorage.getItem('username');
          const userAvatar = localStorage.getItem('userAvatar');
          
          // Update username and role
          const userNameElement = document.querySelector('.user-name');
          const userRoleElement = document.querySelector('.user-role');
          if (userNameElement) userNameElement.textContent = `Welcome, ${userName || 'User'}`;
          if (userRoleElement) userRoleElement.textContent = userRole || 'Role';
          
          // Update all user photos on the page
          const userPhotos = document.querySelectorAll('.user-photo');
          userPhotos.forEach(photo => {
              if (userAvatar) {
                  photo.src = userAvatar;
              } else {
                  photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'; // Default photo
              }
          });
      }
      
      document.addEventListener("DOMContentLoaded", function() {
  // Load project status data
  loadStatus();
  
  // Load overall project pie chart
  loadOverallProjectPieChart();
  
  // Load project summary
  loadProjectSummary();
  
  // Set up user info
  const username = localStorage.getItem('username');
  const role = localStorage.getItem('role');
  const userAvatar = localStorage.getItem('userAvatar');
  
  if (username) {
    document.querySelector('.user-name').textContent = `Welcome, ${username}`;
  }
  
  if (role) {
    document.querySelector('.user-role').textContent = role;
  }
  
  if (userAvatar) {
    const userPhotoElements = document.querySelectorAll('.user-photo');
    userPhotoElements.forEach(element => {
      element.src = userAvatar;
    });
  }
});

// Add this function to initialize edit mode state for all tables
function initializeEditModeState() {
  console.log("Initializing edit mode state for all tables");
  
  // Make sure all tables start in non-edit mode
  const tableContainers = document.querySelectorAll('.table-container');
  console.log(`Found ${tableContainers.length} table containers`);
  
  tableContainers.forEach(container => {
    // Remove edit-mode class if it exists
    container.classList.remove('edit-mode');
    
    // Set button to Edit mode
    const editButton = container.querySelector('.toggle-edit-mode-btn');
    if (editButton) {
      console.log("Setting button to Edit mode");
      editButton.innerHTML = '<i class="fas fa-edit"></i> Edit';
      editButton.classList.remove('btn-primary');
      editButton.classList.add('btn-secondary');
    } else {
      console.log("Edit button not found in container");
    }
  });
}

// Call this function after loading the status
async function loadStatus() {
  // ... existing code ...
  
  // After all tables are loaded, initialize edit mode state
  initializeEditModeState();
  
  // Hide table actions for non-privileged users
  hideTableActions(); // Add this line here
}

// Add this to ensure edit mode is initialized after everything is loaded
window.onload = function() {
  console.log("Window loaded, initializing edit mode state");
  setTimeout(initializeEditModeState, 500); // Give a bit more time for everything to render
};

      // Call this function when the page loads
      document.addEventListener('DOMContentLoaded', updateUserInterface);
      
      // Optional: Refresh user interface periodically to catch any changes
      setInterval(updateUserInterface, 30000); // Update every 30 seconds
      </script>    
  </body>
</html>
