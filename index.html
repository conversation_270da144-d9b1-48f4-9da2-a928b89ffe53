<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Composure Dashboard</title>
    <link rel="stylesheet" href="/styles/style.css" />
    <link rel="stylesheet" href="/styles/styles.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />
    <!-- Bootstrap CSS -->
    <link
      href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <style>
      .admin-only {
        display: none; /* Hidden by default */
      }
      
      /* Only show for admin users */
      body[data-role="Admin"] .admin-only {
        display: block;
      }      
      
      /* Notification Toast */
      .toast-notification {
          position: fixed;
          bottom: 20px;
          right: 20px;
          background-color: #28a745;
          color: white;
          padding: 15px 20px;
          border-radius: 5px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          z-index: 1050;
          opacity: 0;
          visibility: hidden;
          transition: opacity 0.3s, visibility 0.3s, transform 0.3s;
          transform: translateY(20px);
      }
      .toast-notification.show {
          opacity: 1;
          visibility: visible;
          transform: translateY(0);
      }

      /* Enhanced My Tickets Container Styling */
      .my-tickets-container {
        margin-top: 20px;
        margin-bottom: 30px;
      }

      .my-tickets-container .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        overflow: hidden;
      }

      .my-tickets-container .card:hover {
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
      }

      .my-tickets-container .card-header {
        background: linear-gradient(135deg, #006666 0%, #004444 100%);
        color: white;
        border: none;
        padding: 20px 25px;
        border-radius: 12px 12px 0 0;
      }

      .my-tickets-container .card-header h4 {
        margin: 0;
        font-size: 1.4rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .my-tickets-container .card-header h4::before {
        
        font-size: 1.2rem;
      }

      .my-tickets-container .card-body {
        padding: 0;
      }

      /* Enhanced Table Styling */
      .my-tickets-container .table {
        margin: 0;
        border-collapse: separate;
        border-spacing: 0;
      }

      .my-tickets-container .table thead th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        padding: 15px 12px;
        border: none;
        border-bottom: 2px solid #dee2e6;
        position: sticky;
        top: 0;
        z-index: 10;
      }

      .my-tickets-container .table tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #f1f3f4;
      }

      .my-tickets-container .table tbody tr:hover {
        background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
        transform: scale(1.01);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .my-tickets-container .table tbody td {
        padding: 12px;
        vertical-align: middle;
        border: none;
        font-size: 0.9rem;
        color: #495057;
      }

      /* Enhanced Status Badge Styling */
      .my-tickets-container .badge {
        padding: 6px 12px;
        font-size: 0.75rem;
        font-weight: 600;
        border-radius: 20px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .my-tickets-container .badge-warning {
        background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
        color: #fff;
      }

      .my-tickets-container .badge-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: #fff;
      }

      .my-tickets-container .badge-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        color: #fff;
      }

      .my-tickets-container .badge-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
        color: #fff;
      }

      /* Enhanced Action Button Styling */
      .my-tickets-container .btn-primary {
        background: linear-gradient(135deg, #006666 0%, #004444 100%);
        border: none;
        border-radius: 8px;
        padding: 6px 16px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .my-tickets-container .btn-primary:hover {
        background: linear-gradient(135deg, #004444 0%, #003333 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      /* Enhanced Loading and Empty States */
      .my-tickets-container .text-center {
        padding: 40px 20px;
        color: #6c757d;
        font-style: italic;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 8px;
        margin: 20px;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .my-tickets-container .table-responsive {
          border-radius: 8px;
          overflow: hidden;
        }
        
        .my-tickets-container .table thead th,
        .my-tickets-container .table tbody td {
          padding: 8px 6px;
          font-size: 0.8rem;
        }
        
        .my-tickets-container .card-header h4 {
          font-size: 1.2rem;
        }
      }

      /* Animation for new tickets */
      @keyframes slideInFromTop {
        0% {
          opacity: 0;
          transform: translateY(-20px);
        }
        100% {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .my-tickets-container .table tbody tr {
        animation: slideInFromTop 0.3s ease-out;
      }

      /* Enhanced table header with subtle border */
      .my-tickets-container .table thead {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      }

      /* Better spacing for table content */
      .my-tickets-container .table-responsive {
        border-radius: 0 0 12px 12px;
        overflow: hidden;
      }

      /* Refresh Button Styling */
      .refresh-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        border-radius: 8px;
        padding: 8px 12px;
        transition: all 0.3s ease;
      }

      .refresh-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: rotate(180deg);
        color: white;
      }

      .refresh-btn i {
        transition: transform 0.3s ease;
      }

      /* Search and Filter Controls */
      .tickets-controls {
        padding: 10px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #dee2e6;
      }

      .search-box .input-group {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .search-box .input-group-text {
        background: linear-gradient(135deg, #006666 0%, #004444 100%);
        color: white;
        border: none;
        padding: 10px 15px;
      }

      .search-box .form-control {
        border: none;
        padding: 10px 15px;
        font-size: 0.9rem;
      }

      .search-box .form-control:focus {
        box-shadow: none;
        border-color: #006666;
      }

      .tickets-controls select {
        border-radius: 8px;
        border: 1px solid #dee2e6;
        padding: 0px;
        font-size: 0.9rem;
        background: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .tickets-controls select:focus {
        border-color: #006666;
        box-shadow: 0 0 0 0.2rem rgba(0, 102, 102, 0.25);
      }

      /* Pagination Info */
      .pagination-info {
        padding: 0 25px;
        color: #6c757d;
        font-size: 0.85rem;
      }

      /* Sortable Headers */
      .sortable {
        cursor: pointer;
        user-select: none;
        position: relative;
        transition: all 0.2s ease;
      }

      .sortable:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
      }

      .sortable i {
        margin-left: 5px;
        opacity: 0.5;
        transition: all 0.2s ease;
      }

      .sortable.sort-asc i {
        opacity: 1;
        transform: rotate(180deg);
      }

      .sortable.sort-desc i {
        opacity: 1;
        transform: rotate(0deg);
      }

      .sortable:hover i {
        opacity: 0.8;
      }

      /* Pagination Controls */
      .pagination-controls {
        padding: 0 25px 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-top: 1px solid #dee2e6;
      }

      .pagination-controls .pagination {
        margin: 0;
      }

      .pagination-controls .page-link {
        border: none;
        color: #006666;
        padding: 8px 12px;
        margin: 0 2px;
        border-radius: 6px;
        transition: all 0.2s ease;
      }

      .pagination-controls .page-link:hover {
        background: linear-gradient(135deg, #006666 0%, #004444 100%);
        color: white;
        transform: translateY(-1px);
      }

      .pagination-controls .page-item.active .page-link {
        background: linear-gradient(135deg, #006666 0%, #004444 100%);
        border: none;
        color: white;
      }

      .pagination-controls .page-item.disabled .page-link {
        color: #6c757d;
        background: transparent;
      }

      #page-size-select {
        border-radius: 6px;
        border: 1px solid #dee2e6;
        padding: 6px 10px;
        font-size: 0.85rem;
      }

      /* Loading Animation for Refresh */
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .refresh-btn.loading i {
        animation: spin 1s linear infinite;
      }

      /* Responsive adjustments for new features */
      @media (max-width: 768px) {
        .tickets-controls .row {
          margin: 0;
        }
        
        .tickets-controls .col-md-6,
        .tickets-controls .col-md-3 {
          margin-bottom: 10px;
        }
        
        .pagination-controls {
          flex-direction: column;
          gap: 10px;
          align-items: center;
        }
      }
    </style>    
    <script>
      function getAuthHeaders() {
  const token = localStorage.getItem('token');
  return token ? { 'Authorization': 'Bearer ' + token } : {};
}
      // Function to get the success icon URL
      async function getSuccessIconUrl() {
        try {
          const token = localStorage.getItem("token");
          const response = await fetch('/api/success-icon-url', {
            headers: { Authorization: `Bearer ${token}` }
          });
          if (response.ok) {
            const data = await response.json();
            return data.url;
          }
          return 'success.png'; // Fallback
        } catch (error) {
          console.error('Error fetching success icon URL:', error);
          return 'success.png'; // Fallback
        }
      }

      // Function to update all success icons
      async function updateSuccessIcons() {
        try {
          const successIconUrl = await getSuccessIconUrl();
          console.log('Success icon URL:', successIconUrl);
          
          // Update all success.png images
          const successIcons = document.querySelectorAll('img[data-asset-name="success.png"]');
          successIcons.forEach(icon => {
            icon.src = successIconUrl;
            
            // Add error handler
            icon.onerror = function() {
              console.warn(`Failed to load image from ${successIconUrl}, falling back to local`);
              this.src = 'success.png';
              this.onerror = null; // Prevent infinite loop
            };
          });
          
          console.log(`Updated ${successIcons.length} success icons`);
        } catch (error) {
          console.error('Error updating success icons:', error);
        }
      }

      // Call this function when the DOM is loaded
      document.addEventListener('DOMContentLoaded', updateSuccessIcons);
    </script>
  </head>
  <body>
    <div class="toast-notification" id="toast-notification"></div>
    <button class="sidebar-toggle" id="sidebarToggle">
      <i class="fas fa-bars"></i>
    </button>
    <div class="container-fluid"> 
      <div class="row" style="margin: 0; width: 100%;"> <!-- Added style here -->
        <div class="sidebar">
          <div class="dashboard-header">
            <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1747737528/logo_i76d4f.jpg" alt="Logo" class="sidebar-logo">
            <h2>Dashboards</h2>
          </div>
          <hr class="divider" />
          
          <nav>
            <h3>PROJECT LIST</h3>
            
            <!-- Moved search bar under PROJECT LIST heading -->
            <div class="sidebar-search">
              <div class="input-group">
                <input type="text" id="project-search" class="form-control form-control-sm" placeholder="Search by Project ID...">
                <div class="input-group-append">
                  <button class="btn btn-sm btn-outline-secondary" type="button" id="clear-search">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
            
            <!-- Moved category selector under PROJECT LIST heading -->
            <div class="sidebar-category mt-3 mb-2">
              <select id="project-category" class="form-control form-control-sm">
                <option value="all">All Projects</option>
                <option value="client" selected>By Client</option>
              </select>
            </div>
            
            <div id="project-list-container">
              <ul id="project-list">
                <!-- Project items will be dynamically loaded here -->
              </ul>
            </div>
            
            <!-- Client category view (initially hidden) -->
            <div id="client-category-container" style="display: none;">
              <div id="client-accordion" class="accordion">
                <!-- Client categories will be dynamically loaded here -->
              </div>
            </div>
          </nav>
          <!-- <button onclick="window.location.href='/new-project.html'">
            Add New Project
          </button>-->
        </div>
        <div class="main-wrapper" style="padding-top: 60px; overflow-y: auto; height: calc(100vh - 60px);"> <!-- Fixed height and scrolling only for content -->
          <header class="top-bar">
            <div class="left-section">
              <button onclick="goHome()" class="home-button">
                <i class="fas fa-home"></i> <span class="home-text">Home</span>
              </button>
              <!-- Add the dropdowns here -->
              <div class="dropdown-container">
                <div class="dropdown">
                  <button class="dropdown-button">Production</button>
                  <div class="dropdown-content">
                    <a href="Move-to-Production.html">Move to Production</a>
                  </div>
                </div>
                <div class="dropdown">
                  <button class="dropdown-button">Reports</button>
                  <div class="dropdown-content">
                    <a href="WIP-report.html">WIP Report</a>
                  </div>
                </div>
                <div class="dropdown">
                  <button class="dropdown-button">Project Management</button>
                  <div class="dropdown-content">
                    <a href="#" id="estimates-link">Estimates</a>
                    <a href="Invoices.html">Invoice</a>
                  </div>
                </div>
                <div class="dropdown">
                  <button class="dropdown-button">Tickets</button>
                  <div class="dropdown-content">
                    <a href="Tickets.html">Tickets</a>
                  </div>
                </div>
              </div>
            </div>
            <!-- End the dropdowns here -->
            <div class="right-section">
              <div class="user-info">
                <div class="user-photo-container">
                  <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp" alt="User Photo" class="user-photo" id="userAvatar" onerror="this.onerror=null;this.src='https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';">
                  <div class="user-dropdown-content">
                    <a href="/user-info.html"><i class="fas fa-cog"></i> Settings</a>
                    <a href="/AuditLogs.html" class="admin-only"><i class="fas fa-history"></i> Audit Logs</a>
                  </div>
                </div>
                <div class="user-details">
                  <span class="user-name" id="username">Welcome, User</span>
                  <span class="user-role" id="userRole">Role</span>
                </div>
              </div>
              <button onclick="logout()" class="logout-button">Logout</button>
            </div>
          </header>
        <div class="main-content" id="main-content">
          <div class="dashboard-column-layout">
            <!-- Main Column -->
            <div class="dashboard-main-column">
              <!--Test new boxes-->

              <div class="row top-containers admin-only"> 
                <div class="col-12">
                  <div class="row"> 
                    <div class="col-md-3">
                      <div class="card o-hidden small-widget">
                        <div class="card-body total-project border-b-primary border-2"><span class="f-light f-w-500 f-14">Total Project</span>
                          <div class="project-details"> 
                            <div id="project-counter-total-project" class="project-counter">
                              <h2 class="f-w-600">Loading...</h2>                              
                            </div>
                            <div class="product-sub bg-primary-light">                
                                <img src="success.png" alt="invoice-icon" class="invoice-icon" data-asset-name="success.png"/>                
                            </div>
                          </div>
                          <ul class="bubbles">
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="card o-hidden small-widget">
                        <div class="card-body total-Progress border-b-warning border-2"><span class="f-light f-w-500 f-14">Yet to Start</span>
                          <div class="project-details"> 
                            <div id="project-counter-yettostart-project" class="project-counter">
                              <h2 class="f-w-600">Loading...</h2>
                            </div>
                            <div class="product-sub bg-warning-light">                
                                <img src="success.png" alt="invoice-icon" class="invoice-icon" data-asset-name="success.png"/>                
                            </div>
                          </div>
                          <ul class="bubbles">
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="card o-hidden small-widget">
                        <div class="card-body total-Progress border-b-warning border-2"><span class="f-light f-w-500 f-14">In Progress</span>
                          <div class="project-details"> 
                            <div id="project-counter-inprogress-project" class="project-counter"> 
                              <h2 class="f-w-600">Loading...</h2>
                            </div>
                            <div class="product-sub bg-warning-light">                
                                <img src="success.png" alt="invoice-icon" class="invoice-icon" data-asset-name="success.png"/>                
                            </div>
                          </div>
                          <ul class="bubbles">
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="card o-hidden small-widget">
                        <div class="card-body total-Progress border-b-warning border-2"><span class="f-light f-w-500 f-14">Completed</span>
                          <div class="project-details"> 
                            <div id="project-counter-completed-project" class="project-counter">
                              <h2 class="f-w-600">Loading...</h2>
                            </div>
                            <div class="product-sub bg-warning-light">                
                                <img src="success.png" alt="invoice-icon" class="invoice-icon" data-asset-name="success.png"/>                
                            </div>
                          </div>
                          <ul class="bubbles">
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                            <li class="bubble"></li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    
                  </div>
                </div>
              </div>
              <!--Test new boxes ends-->
              <!-- Add the new boxes here -->
 
              <div class="row">
                <div class="col-12 dashboard-container admin-only">
                  <div class="dashboard-box">
                    <h3>Dashboard Overview</h3>
                    
                    <!-- Charts Grid -->
                    <div class="charts-grid">
                      <div class="chart-container">
                        <h4>Projects by Client</h4>
                        <canvas id="clientBarChart"></canvas>
                      </div>
                      <div class="chart-container">
                        <h4>Projects by Project Manager</h4>
                        <canvas id="projectManagerBarChart"></canvas>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="row">
                <div class="col-12"> <!-- Match the same column width as dashboard -->
                  <div class="my-tickets-container">
                    <div class="card">
                      <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                          <h4 id="tickets-header">My Tickets</h4>
                          <button class="btn btn-light btn-sm refresh-btn" onclick="loadMyTickets()" title="Refresh Tickets">
                            <i class="fas fa-sync-alt"></i>
                          </button>
                        </div>
                      </div>
                      <div class="card-body">
                        <!-- Search and Filter Section -->
                        <div class="tickets-controls mb-3">
                          <div class="row">
                            <div class="col-md-6">
                              <div class="search-box">
                                <div class="input-group">
                                  <div class="input-group-prepend">
                                    <span class="input-group-text">
                                      <i class="fas fa-search"></i>
                                    </span>
                                  </div>
                                  <input type="text" class="form-control" id="ticket-search-input" placeholder="Search tickets...">
                                </div>
                              </div>
                            </div>
                            <div class="col-md-3">
                              <select class="form-control" id="status-filter-select">
                                <option value="">All Status</option>
                                <option value="open-inprogress" selected>Open & In Progress</option>
                                <option value="resolved">Resolved</option>
                              </select>
                            </div>
                            <div class="col-md-3">
                              <select class="form-control" id="role-filter-select">
                                <option value="">All Roles</option>
                                <option value="Project Manager">Project Manager</option>
                                <option value="Assigned To">Assigned To</option>
                                <option value="Supporting Team">Supporting Team</option>
                              </select>
                            </div>
                          </div>
                        </div>
                        
                        <!-- Pagination Info -->
                        <div class="pagination-info mb-2">
                          <small class="text-muted">
                            Showing <span id="showing-start">1</span> to <span id="showing-end">10</span> of <span id="total-tickets-count">0</span> tickets
                          </small>
                        </div>
                        
                        <div class="table-responsive">
                          <table class="table table-hover" id="my-tickets-table">
                            <thead>
                              <tr>
                                <th class="sortable" data-sort="ticketNumber">
                                  Ticket ID <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-sort="projectId">
                                  Project ID <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-sort="projectName">
                                  Project Name <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-sort="ticketHeading">
                                  Ticket Heading <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-sort="dueDate">
                                  Due Date <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-sort="status">
                                  Status <i class="fas fa-sort"></i>
                                </th>
                                <th class="sortable" data-sort="userRole">
                                  Your Role <i class="fas fa-sort"></i>
                                </th>
                                <th>Action</th>
                              </tr>
                            </thead>
                            <tbody id="my-tickets-list">
                              <!-- Tickets will be populated here -->
                            </tbody>
                          </table>
                        </div>
                        
                        <!-- Pagination Controls -->
                        <div class="pagination-controls mt-3">
                          <div class="d-flex justify-content-between align-items-center">
                            <div class="pagination-info">
                              <select class="form-control form-control-sm" id="page-size-select" style="width: auto;">
                                <option value="5">5 per page</option>
                                <option value="10" selected>10 per page</option>
                                <option value="20">20 per page</option>
                                <option value="50">50 per page</option>
                              </select>
                            </div>
                            <nav aria-label="Tickets pagination">
                              <ul class="pagination pagination-sm mb-0" id="pagination-controls">
                                <!-- Pagination buttons will be generated here -->
                              </ul>
                            </nav>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="dashboard-side-column">
              <div class="vl">
                <div class="card add-project-link" style="display: none;">
                  <div class="categories"></div>
                  <div class="categories-content">
                    <span class="text-truncate f-12 d-block mb-2">Let's add work to your space</span>
                    <a href="/new-project.html">+Add Project</a>
                  </div>
                </div>
                <div class="activity-sidebar">                
                  <div class="activity-tracking-card">
                    <div class="activity-tracking-header">
                      <h4>Activity Feed</h4>
                    </div>
                    <div class="activity-tracking-body">
                      <div class="activity-items">
                        <!-- Activity items will be dynamically added here -->
                        <div class="coming-soon-banner">
                          <i class="fas fa-hourglass-half"></i> Coming Soon
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            </div>
          </div>
        </div>
    <!-- Bootstrap JS and dependencies -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
      let currentProjectId = null;

      document.addEventListener("DOMContentLoaded", function() {
        loadProjects();
        loadMyTickets();
        
        // Add event listeners for search and category selection
        const searchInput = document.getElementById('project-search');
        const clearSearchBtn = document.getElementById('clear-search');
        const categorySelect = document.getElementById('project-category');
        
        searchInput.addEventListener('input', filterProjects);
        clearSearchBtn.addEventListener('click', clearSearch);
        categorySelect.addEventListener('change', toggleCategoryView);

        // Set "By Client" as default
        document.getElementById('project-category').value = 'client';
        toggleCategoryView();
      });

      // Store all projects for filtering
      let allProjects = [];

      async function loadProjects() {
        const projectList = document.getElementById("project-list");
        projectList.innerHTML = "<li>Loading projects...</li>";

        try {
          const token = localStorage.getItem("token");
          const userRole = localStorage.getItem("role");
          const username = localStorage.getItem("username");
          
          const response = await fetch("/api/inprogressprojects", {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
              alert("Session expired or Unauthorized. Please login again.");
              window.location.href = "/login.html";
              return;
            }
            throw new Error(`Failed to fetch project details: ${response.statusText}`);
          }

          const projects = await response.json();
          console.log("Projects fetched:", projects); // Debugging
          
          // Filter projects based on user role
          let filteredProjects = projects;
          
          if (userRole !== "Admin") {
            // Filter projects where the user is involved
            filteredProjects = projects.filter(project => {
              return (
                project.projectManager === username || 
                project.creativeServiceLead === username || 
                project.creativeAcquisitionLead === username ||
                (Array.isArray(project.supportingTeam) && project.supportingTeam.includes(username))
              );
            });
          }
          
          // Store projects globally for filtering
          allProjects = filteredProjects;

          if (filteredProjects.length === 0) {
            projectList.innerHTML = "<li>No projects found</li>";
            return;
          }

          // Display projects in the regular list
          displayProjectsList(filteredProjects);
          
          // Also prepare the client category view
          prepareClientCategoryView(filteredProjects);
          
        } catch (error) {
          console.error("Error loading projects:", error);
          projectList.innerHTML = "<li>Error loading projects</li>";
        }
      }

      function displayProjectsList(projects) {
        const projectList = document.getElementById("project-list");
        projectList.innerHTML = ""; // Clear the list
        
        projects.forEach((project) => {
          const li = document.createElement("li");
          const a = document.createElement("a");
          a.href = `Project-details.html?projectId=${project._id}`;
          a.textContent = `${project._id} - ${project.title}`;
          a.title = `${project._id} - ${project.title}`; // Tooltip for full name
          li.appendChild(a);
          projectList.appendChild(li);
        });
      }

      function prepareClientCategoryView(projects) {
        const clientAccordion = document.getElementById("client-accordion");
        clientAccordion.innerHTML = ""; // Clear existing content
        
        // Group projects by client
        const clientGroups = {};
        
        projects.forEach(project => {
          const clientName = project.clientName || "Unassigned";
          
          if (!clientGroups[clientName]) {
            clientGroups[clientName] = [];
          }
          
          clientGroups[clientName].push(project);
        });
        
        // Sort clients alphabetically
        const sortedClients = Object.keys(clientGroups).sort();
        
        // Create a simple list for clients instead of accordion
        const clientList = document.createElement('ul');
        clientList.id = 'client-list';
        
        sortedClients.forEach(client => {
          const clientProjects = clientGroups[client];
          const projectCount = clientProjects.length;
          
          // Create client list item
          const li = document.createElement('li');
          const a = document.createElement('a');
          a.href = "#";
          a.innerHTML = `${client} <span class="project-count">${projectCount}</span>`;
          a.setAttribute("data-client", client);
          a.setAttribute("onclick", `toggleClientProjects('${client}')`);
          li.appendChild(a);
          
          // Create hidden projects list for this client
          const projectsList = document.createElement('ul');
          projectsList.className = 'client-projects';
          projectsList.id = `client-projects-${client.replace(/\s+/g, '').replace(/[^a-zA-Z0-9]/g, '')}`;
          projectsList.style.display = 'none';
          
          clientProjects.forEach(project => {
            const projectLi = document.createElement('li');
            projectLi.className = 'client-project-item';
            const projectA = document.createElement('a');
            projectA.href = `Project-details.html?projectId=${project._id}`;
            projectA.textContent = `${project._id} - ${project.title}`;
            projectA.title = `${project._id} - ${project.title}`; // Add title for tooltip
            projectLi.appendChild(projectA);
            projectsList.appendChild(projectLi);
          });
          
          li.appendChild(projectsList);
          clientList.appendChild(li);
        });
        
        clientAccordion.appendChild(clientList);
      }

      function toggleClientProjects(client) {
        const clientId = client.replace(/\s+/g, '').replace(/[^a-zA-Z0-9]/g, '');
        const projectsList = document.getElementById(`client-projects-${clientId}`);
        
        // Toggle the display of this client's projects
        if (projectsList.style.display === 'none') {
          // Hide all other client projects first
          document.querySelectorAll('.client-projects').forEach(list => {
            list.style.display = 'none';
          });
          
          // Show this client's projects
          projectsList.style.display = 'block';
          
          // Highlight the selected client
          document.querySelectorAll('#client-list > li > a').forEach(a => {
            a.classList.remove('active');
          });
          document.querySelector(`#client-list a[data-client="${client}"]`).classList.add('active');
        } else {
          // Toggle off if already showing
          projectsList.style.display = 'none';
          document.querySelector(`#client-list a[data-client="${client}"]`).classList.remove('active');
        }
      }

      function filterProjects() {
        const searchTerm = document.getElementById('project-search').value.toLowerCase();
        const categoryView = document.getElementById('project-category').value;
        
        if (searchTerm === '') {
          // If search is empty, show all projects
          if (categoryView === 'all') {
            displayProjectsList(allProjects);
          } else {
            // Just refresh the client view without filtering
            prepareClientCategoryView(allProjects);
          }
          return;
        }
        
        // Filter projects by ID
        const filteredProjects = allProjects.filter(project => 
          project._id.toLowerCase().includes(searchTerm)
        );
        
        if (categoryView === 'all') {
          // Update the regular list view
          displayProjectsList(filteredProjects);
        } else {
          // Update the client category view
          prepareClientCategoryView(filteredProjects);
        }
      }

      function clearSearch() {
        document.getElementById('project-search').value = '';
        filterProjects(); // This will reset to show all projects
      }

      function toggleCategoryView() {
        const categoryView = document.getElementById('project-category').value;
        const projectListContainer = document.getElementById('project-list-container');
        const clientCategoryContainer = document.getElementById('client-category-container');
        
        if (categoryView === 'all') {
          projectListContainer.style.display = 'block';
          clientCategoryContainer.style.display = 'none';
        } else {
          projectListContainer.style.display = 'none';
          clientCategoryContainer.style.display = 'block';
        }
        
        // Apply any current search filter
        filterProjects();
      }

      function goHome() {
        window.location.href = "index.html";
      }

      function logout() {
        // Clear all user-related data
        const itemsToClear = ['token', 'username', 'role', 'userAvatar', 'userId'];
        itemsToClear.forEach(item => localStorage.removeItem(item));
        
        // Reset any user photos to default before redirecting
        const userPhotos = document.querySelectorAll('.user-photo');
        userPhotos.forEach(photo => {
          photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
        });
        
        // Redirect to login page
        window.location.href = '/login.html';
      }
    </script>
    <script>
      document.addEventListener('DOMContentLoaded', function () {
        async function fetchProjectCount() {
          try {
            const token = localStorage.getItem("token");
            const response = await fetch('/project-count', {
              headers: { Authorization: `Bearer ${token}` }
            });
            const data = await response.json();

            // ✅ Target the <h2> inside the project-counter
            const countElement = document.querySelector('#project-counter-yettostart-project h2');
            if (countElement) {
              countElement.innerText = data.count;
            }
          } catch (error) {
            console.error('Error fetching project count:', error);

            // Show error message in <h2> if it exists
            const countElement = document.querySelector('#project-counter-yettostart-project h2');
            if (countElement) {
              countElement.innerText = 'Error';
            }
          }
        }

        fetchProjectCount();
      });
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const userNameElement = document.querySelector(".user-name");
        const userRoleElement = document.querySelector(".user-role");

        // Retrieve the user's role and name from localStorage
        const userRole = localStorage.getItem("role");
        const userName = localStorage.getItem("username"); // Retrieve the username from localStorage

        // Update the user profile section
        if (userNameElement) {
          userNameElement.textContent = `Welcome, ${userName || "User"}`;
        }
        if (userRoleElement) {
          userRoleElement.textContent = userRole || "Role";
        }

        console.log("User Role:", userRole); // Debugging
        console.log("User Name:", userName); // Debugging
      });
    </script>
    <script>
      async function navigateToNewProject() {
        const token = localStorage.getItem("token"); // Retrieve the token
        console.log("Token being sent:", token); // Debugging

        if (!token) {
          alert("Unauthorized access. Please log in.");
          window.location.href = "/login.html";
          return;
        }

        try {
          const response = await fetch("/new-project.html", {
            headers: {
              Authorization: `Bearer ${token}`, // Send the token in the Authorization header
            },
          });

          if (response.ok) {
            window.location.href = "/new-project.html";
          } else {
            throw new Error("Unauthorized access");
          }
        } catch (error) {
          console.error("Failed to navigate to new project:", error);
          alert("Unauthorized access. Please log in.");
          window.location.href = "/login.html";
        }
      }
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const userRole = localStorage.getItem("role"); // Retrieve the user's role
        const addProjectLink = document.querySelector(".add-project-link"); // Select the container    
        const editButton = document.querySelector(".edit-button"); // Select the Edit button
        const deleteButton = document.querySelector(".delete-button"); // Select the Edit button
        
        if (userRole === "Admin" || userRole === "Project Manager") {
          if (addProjectLink) addProjectLink.style.display = "block";
          if (editButton) editButton.style.display = "inline-block";
          if (deleteButton) deleteButton.style.display = "inline-block";
        } else {
          if (addProjectLink) addProjectLink.style.display = "none";
          if (editButton) editButton.style.display = "none";
          if (deleteButton) deleteButton.style.display = "none";
        }

        console.log("User Role:", userRole); // Debugging
      });
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const userRole = localStorage.getItem("role"); // Retrieve the user's role
        const moveToCompletedContainer = document.getElementById("move-to-completed-container");

        if (moveToCompletedContainer) {
        if (userRole === "Admin" || userRole === "Project Manager") {
          moveToCompletedContainer.style.display = "block"; // Show the button container
        } else {
          moveToCompletedContainer.style.display = "none"; // Hide the button container
        }
        }
      });
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", async function () {
        async function fetchCompletedProjectCount() {
          try {
            const response = await fetch('/api/completed-project-count', {
              headers: getAuthHeaders()
            });
            if (!response.ok) {
              throw new Error("Failed to fetch completed project count");
            }

            const data = await response.json();

            // ✅ Target the <h2> inside the project-counter-completed-project
            const countElement = document.querySelector("#project-counter-completed-project h2");
            if (countElement) {
              countElement.innerText = data.count; // Set the count
            }
          } catch (error) {
            console.error("Error fetching completed project count:", error);

            // Show error message in <h2> if it exists
            const countElement = document.querySelector("#project-counter-completed-project h2");
            if (countElement) {
              countElement.innerText = "Error";
            }
          }
        }

        fetchCompletedProjectCount();
      });
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", async function () {
        async function fetchTotalProjectCount() {
          const response = await fetch('/api/total-project-count', {
            headers: getAuthHeaders()
          });
          if (response.status === 401) {
            alert('Session expired. Please log in again.');
            window.location.href = '/login.html';
            return;
          }
          try {
            const data = await response.json();

            // ✅ Target the <h2> inside the project-counter-total-project
            const countElement = document.querySelector("#project-counter-total-project h2");
            if (countElement) {
              countElement.innerText = data.totalCount; // Set the total count
            }
          } catch (error) {
            console.error("Error fetching total project count:", error);

            // Show error message in <h2> if it exists
            const countElement = document.querySelector("#project-counter-total-project h2");
            if (countElement) {
              countElement.innerText = "Error";
            }
          }
        }

        fetchTotalProjectCount();
      });
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", async function () {
        // Fetch data for client-based chart
        async function fetchClientData() {
          const response = await fetch('/api/inprogressprojects-by-client', {
            headers: getAuthHeaders()
          });
          if (response.status === 401) {
            alert('Session expired. Please log in again.');
            window.location.href = '/login.html';
            return;
          }
          try {
            const data = await response.json();
            console.log("Client Data:", data); // Debugging
            return data;
          } catch (error) {
            console.error("Error fetching client data:", error);
            return [];
          }
        }

        // Fetch data for project manager-based chart
        async function fetchProjectManagerData() {
          const response = await fetch('/api/inprogressprojects-by-projectmanager', {
            headers: getAuthHeaders()
          });
          if (response.status === 401) {
            alert('Session expired. Please log in again.');
            window.location.href = '/login.html';
            return;
          }
          try {
            const data = await response.json();
            console.log("Project Manager Data:", data);
            return data;
          } catch (error) {
            console.error("Error fetching project manager data:", error);
            // Return empty array to prevent rendering errors
            return [];
          }
        }

        // Add retry logic for critical API calls
        async function fetchWithRetry(url, options = {}, maxRetries = 3) {
          let retries = 0;
          
          while (retries < maxRetries) {
            try {
              const response = await fetch(url, options);
              
              if (response.ok) {
                return response;
              }
              
              // If server is unavailable, wait and retry
              if (response.status === 503) {
                retries++;
                console.log(`Service unavailable, retrying (${retries}/${maxRetries})...`);
                await new Promise(resolve => setTimeout(resolve, 2000 * retries));
                continue;
              }
              
              // For other errors, just return the response
              return response;
            } catch (error) {
              retries++;
              console.error(`Fetch error (${retries}/${maxRetries}):`, error);
              
              if (retries >= maxRetries) {
                throw error;
              }
              
              // Wait before retrying
              await new Promise(resolve => setTimeout(resolve, 2000 * retries));
            }
          }
        }

        // Render bar chart
        function renderBarChart(canvasId, labels, data, label) {
          const ctx = document.getElementById(canvasId)?.getContext("2d");
          if (!ctx) {
            console.error(`Canvas with ID "${canvasId}" not found.`);
            return;
          }

          // Modern color palette
          const colors = {
            client: {
              backgroundColor: [
                'rgba(71, 179, 156, 0.7)',  // Teal
                'rgba(103, 116, 236, 0.7)', // Purple
                'rgba(252, 196, 25, 0.7)',  // Yellow
                'rgba(255, 110, 118, 0.7)', // Coral
                'rgba(95, 209, 249, 0.7)'   // Sky Blue
              ],
              borderColor: [
                'rgba(71, 179, 156, 1)',
                'rgba(103, 116, 236, 1)',
                'rgba(252, 196, 25, 1)',
                'rgba(255, 110, 118, 1)',
                'rgba(95, 209, 249, 1)'
              ]
            },
            manager: {
              backgroundColor: [
                'rgba(255, 145, 73, 0.7)',  // Orange
                'rgba(95, 209, 249, 0.7)',  // Sky Blue
                'rgba(175, 91, 205, 0.7)',  // Purple
                'rgba(71, 179, 156, 0.7)',  // Teal
                'rgba(252, 196, 25, 0.7)'   // Yellow
              ],
              borderColor: [
                'rgba(255, 145, 73, 1)',
                'rgba(95, 209, 249, 1)',
                'rgba(175, 91, 205, 1)',
                'rgba(71, 179, 156, 1)',
                'rgba(252, 196, 25, 1)'
              ]
            }
          };

          // Choose color set based on chart type
          const colorSet = canvasId.includes('client') ? colors.client : colors.manager;

          new Chart(ctx, {
            type: "bar",
            data: {
              labels: labels,
              datasets: [
                {
                  label: label,
                  data: data,
                  backgroundColor: colorSet.backgroundColor.slice(0, data.length),
                  borderColor: colorSet.borderColor.slice(0, data.length),
                  borderWidth: 1,
                  borderRadius: 6,
                  maxBarThickness: 50,  // Prevent bars from becoming too wide
                  minBarLength: 10
                },
              ],
            },
            options: {
              responsive: true,
              maintainAspectRatio: true,
              aspectRatio: 1.5,  // Adjust this value to control height/width ratio
              scales: {
                y: {
                  beginAtZero: true,
                  grid: {
                    display: true,
                    color: 'rgba(0, 0, 0, 0.1)',
                  },
                  ticks: {
                    precision: 0
                  }
                },
                x: {
                  grid: {
                    display: false
                  }
                }
              },
              plugins: {
                legend: {
                  display: false  // Hide legend since it's redundant with the title
                },
                tooltip: {
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  titleColor: '#333',
                  bodyColor: '#666',
                  borderColor: 'rgba(0, 0, 0, 0.1)',
                  borderWidth: 1,
                  padding: 10,
                  boxPadding: 4,
                  usePointStyle: true,
                  callbacks: {
                    label: function(context) {
                      return `${context.parsed.y} projects`;
                    }
                  }
                }
              }
            }
          });
        }

        // Fetch and render client-based chart
        const clientData = await fetchClientData();
        const clientLabels = clientData.map((item) => item._id || "Unknown");
        const clientCounts = clientData.map((item) => item.count);
        renderBarChart("clientBarChart", clientLabels, clientCounts, "Projects by Client");

        // Fetch and render project manager-based chart
        const projectManagerData = await fetchProjectManagerData();
        const projectManagerLabels = projectManagerData.map((item) => item._id || "Unknown");
        const projectManagerCounts = projectManagerData.map((item) => item.count);
        renderBarChart(
          "projectManagerBarChart",
          projectManagerLabels,
          projectManagerCounts,
          "Projects by Project Manager"
        );
      });
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", function() {
        const userRole = localStorage.getItem("role");
        const adminElements = document.querySelectorAll('.admin-only');
        
        adminElements.forEach(element => {
          if (userRole === "Admin") {
            element.style.display = "block";
          } else {
            element.style.display = "none";
          }
        });
      });
    </script>
    <script>
      // Add this function to update user info including avatar
      function updateUserInterface() {
          const userRole = localStorage.getItem('role');
          const userName = localStorage.getItem('username');
          const userAvatar = localStorage.getItem('userAvatar');
          
          // Update username and role
          const userNameElement = document.querySelector('.user-name');
          const userRoleElement = document.querySelector('.user-role');
          if (userNameElement) userNameElement.textContent = `Welcome, ${userName || 'User'}`;
          if (userRoleElement) userRoleElement.textContent = userRole || 'Role';
          
          // Update all user photos on the page
          const userPhotos = document.querySelectorAll('.user-photo');
          userPhotos.forEach(photo => {
              if (userAvatar) {
                  photo.src = userAvatar;
              } else {
                  photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'; // Default photo
              }
          });
      }
      
      // Call this function when the page loads
      document.addEventListener('DOMContentLoaded', updateUserInterface);
      
      // Optional: Refresh user interface periodically to catch any changes
      setInterval(updateUserInterface, 30000);
    </script>
    <script>
      // Check if user is logged in
      document.addEventListener('DOMContentLoaded', () => {
        const token = localStorage.getItem('token');
        if (!token) {
          // No token found, redirect to login
          window.location.href = '/login.html';
        }
      });
    </script>
    <script>
      // Immediately set success icons to the Cloudinary URL
      (async function() {
        try {
          // Hardcoded Cloudinary URL based on your cloud name and public ID
          const cloudName = 'dcyeoe53a';
          const successIconUrl = `https://res.cloudinary.com/${cloudName}/image/upload/success_tayqq4`;
          
          // Update all success.png images immediately
          const successIcons = document.querySelectorAll('img[data-asset-name="success.png"]');
          successIcons.forEach(icon => {
            icon.src = successIconUrl;
          });
          
          console.log(`Immediately updated ${successIcons.length} success icons with ${successIconUrl}`);
        } catch (error) {
          console.error('Error in immediate success icon update:', error);
        }
      })();
    </script>
    <script>
      // Function to update background images with Cloudinary URLs
      async function updateBackgroundImages() {
        try {
          // Get the Cloudinary URL for addproject.png
          const response = await fetch('/api/asset-url/addproject.png');
          if (response.ok) {
            const data = await response.json();
            const url = data.url;
            
            // Update the background image for all elements with the categories class
            const categoriesElements = document.querySelectorAll('.add-project-link .categories');
            categoriesElements.forEach(element => {
              element.style.backgroundImage = `url('${url}')`;
            });
            
            console.log('Updated background image with Cloudinary URL:', url);
          }
        } catch (error) {
          console.error('Error updating background images:', error);
        }
      }

      // Call this function when the DOM is loaded
      document.addEventListener('DOMContentLoaded', updateBackgroundImages);
    </script>
    <script>
      // Add this to your existing scripts or update the existing sidebar toggle script
      document.addEventListener('DOMContentLoaded', function() {
        // Sidebar toggle functionality
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.querySelector('.sidebar');
        const topBar = document.querySelector('.top-bar');
        const mainWrapper = document.querySelector('.main-wrapper');
        
        if (sidebarToggle && sidebar) {
          sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
            
            // Adjust top bar position when sidebar is toggled
            if (window.innerWidth <= 1024) {
              if (sidebar.classList.contains('active')) {
                // When sidebar is visible, adjust top bar
                topBar.style.left = '200px';
                topBar.style.width = 'calc(100% - 200px)';
              } else {
                // When sidebar is hidden, make top bar full width
                topBar.style.left = '0';
                topBar.style.width = '100%';
              }
            }
          });
          
          // Close sidebar when clicking outside of it
          document.addEventListener('click', function(event) {
            const isClickInsideSidebar = sidebar.contains(event.target);
            const isClickOnToggle = sidebarToggle.contains(event.target);
            
            if (!isClickInsideSidebar && !isClickOnToggle && sidebar.classList.contains('active')) {
              sidebar.classList.remove('active');
              
              // Reset top bar position
              if (window.innerWidth <= 1024) {
                topBar.style.left = '0';
                topBar.style.width = '100%';
              }
            }
          });
        }
        
        // Handle window resize
        window.addEventListener('resize', function() {
          if (window.innerWidth > 1024) {
            // Reset top bar position for larger screens
            topBar.style.left = '250px';
            topBar.style.width = 'calc(100% - 250px)';
            
            // Remove active class from sidebar if window is resized larger
            if (sidebar.classList.contains('active')) {
              sidebar.classList.remove('active');
            }
          } else if (window.innerWidth > 768 && window.innerWidth <= 1024) {
            // For tablet view
            if (!sidebar.classList.contains('active')) {
              topBar.style.left = '0';
              topBar.style.width = '100%';
            } else {
              topBar.style.left = '200px';
              topBar.style.width = 'calc(100% - 200px)';
            }
          } else {
            // For mobile view
            if (!sidebar.classList.contains('active')) {
              topBar.style.left = '0';
              topBar.style.width = '100%';
            }
          }
        });
        
        // Initialize top bar position on page load
        if (window.innerWidth <= 768) {
          topBar.style.left = '0';
          topBar.style.width = '100%';
        } else if (window.innerWidth <= 1024) {
          // For tablet sizes
          topBar.style.left = '0';
          topBar.style.width = '100%';
        } else {
          topBar.style.left = '250px';
          topBar.style.width = 'calc(100% - 250px)';
        }
      });
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", async function () {
        async function fetchInProgressProjectCount() {
          const response = await fetch('/api/inprogress-project-count', {
            headers: getAuthHeaders()
          });
          if (response.status === 401) {
            alert('Session expired. Please log in again.');
            window.location.href = '/login.html';
            return;
          }
          try {
            const data = await response.json();
            // ✅ Target the <h2> inside the project-counter-inprogress-project
            const countElement = document.querySelector("#project-counter-inprogress-project h2");
            if (countElement) {
              countElement.innerText = data.count; // Set the count
            }
          } catch (error) {
            console.error("Error fetching InProgress project count:", error);
            // Show error message in <h2> if it exists
            const countElement = document.querySelector("#project-counter-inprogress-project h2");
            if (countElement) {
              countElement.innerText = "Error";
            }
          }
        }
        fetchInProgressProjectCount();
      });
    </script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        // Check for URL parameters to show notifications
        const urlParams = new URLSearchParams(window.location.search);
        const toastNotification = document.getElementById('toast-notification');

        if (urlParams.has('project_created')) {
          toastNotification.textContent = 'Project created successfully!';
          toastNotification.classList.add('show');
          setTimeout(() => toastNotification.classList.remove('show'), 5000);
          // Clean up URL
          window.history.replaceState({}, document.title, window.location.pathname);
        } else if (urlParams.has('project_moved')) {
          toastNotification.textContent = 'Project moved to production successfully!';
          toastNotification.classList.add('show');
          setTimeout(() => toastNotification.classList.remove('show'), 5000);
          // Clean up URL
          window.history.replaceState({}, document.title, window.location.pathname);
        }
      });
    </script>
    <script>
      // Add function to load tickets for the current user
      async function loadMyTickets() {
        const ticketsList = document.getElementById("my-tickets-list");
        const refreshBtn = document.querySelector('.refresh-btn');
        if (!ticketsList) return;
        
        // Add loading animation to refresh button
        if (refreshBtn) {
          refreshBtn.classList.add('loading');
        }
        
        ticketsList.innerHTML = "<tr><td colspan='8' class='text-center'>Loading tickets...</td></tr>";

        try {
          const token = localStorage.getItem("token");
          const username = localStorage.getItem("username");
          
          if (!token || !username) {
            ticketsList.innerHTML = "<tr><td colspan='8' class='text-center'>Please login to view tickets</td></tr>";
            return;
          }

          const response = await fetch("/api/tickets", {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
              ticketsList.innerHTML = "<tr><td colspan='8' class='text-center'>Session expired. Please login again.</td></tr>";
              return;
            }
            throw new Error(`Failed to fetch tickets: ${response.statusText}`);
          }

          const allTickets = await response.json();
          console.log("All tickets fetched:", allTickets);
          
          // Filter tickets where the user is involved
          const myTickets = allTickets.filter(ticket => {
            return (
              ticket.projectManager === username || 
              ticket.assignedTo === username ||
              (Array.isArray(ticket.supportingTeam) && ticket.supportingTeam.includes(username))
            );
          });
          
          console.log("My tickets filtered:", myTickets);

          // Store tickets globally for filtering and sorting
          window.allMyTickets = myTickets;
          
          // Update ticket count in header
          updateTicketCount(myTickets.length);
          
          // Apply current filters and display
          applyFiltersAndDisplay();
          
        } catch (error) {
          console.error("Error loading tickets:", error);
          ticketsList.innerHTML = "<tr><td colspan='8' class='text-center'>Error loading tickets</td></tr>";
        } finally {
          // Remove loading animation
          if (refreshBtn) {
            refreshBtn.classList.remove('loading');
          }
        }
      }

      // Global variables for pagination and sorting
      let currentPage = 1;
      let pageSize = 10;
      let currentSort = { field: 'createdDate', direction: 'desc' };
      let filteredTickets = [];

      function updateTicketCount(count) {
        const header = document.getElementById('tickets-header');
        if (header) {
          header.innerHTML = `My Tickets <span class="badge badge-light ml-2">${count}</span>`;
        }
      }

      function applyFiltersAndDisplay() {
        if (!window.allMyTickets) return;
        
        const searchTerm = document.getElementById('ticket-search-input')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('status-filter-select')?.value || '';
        const roleFilter = document.getElementById('role-filter-select')?.value || '';
        
        // Apply filters
        filteredTickets = window.allMyTickets.filter(ticket => {
          const username = localStorage.getItem("username");
          let userRole = "";
          if (ticket.projectManager === username) {
            userRole = "Project Manager";
          } else if (ticket.assignedTo === username) {
            userRole = "Assigned To";
          } else if (Array.isArray(ticket.supportingTeam) && ticket.supportingTeam.includes(username)) {
            userRole = "Supporting Team";
          }
          
          const matchesSearch = !searchTerm || 
            ticket.ticketNumber?.toLowerCase().includes(searchTerm) ||
            ticket.projectId?.toLowerCase().includes(searchTerm) ||
            ticket.projectName?.toLowerCase().includes(searchTerm) ||
            ticket.ticketHeading?.toLowerCase().includes(searchTerm);
            
          let matchesStatus = true;
          if (statusFilter === 'open-inprogress') {
            matchesStatus = ticket.status === 'open' || ticket.status === 'in-progress';
          } else if (statusFilter) {
            matchesStatus = ticket.status === statusFilter;
          }
          const matchesRole = !roleFilter || userRole === roleFilter;
          
          return matchesSearch && matchesStatus && matchesRole;
        });
        
        // Apply sorting
        sortTickets(filteredTickets, currentSort.field, currentSort.direction);
        
        // Reset to first page
        currentPage = 1;
        
        // Display tickets
        displayMyTickets(filteredTickets);
        updatePagination();
      }

      function sortTickets(tickets, field, direction) {
        tickets.sort((a, b) => {
          let aVal = a[field];
          let bVal = b[field];
          
          // Handle date fields
          if (field === 'dueDate' || field === 'createdDate') {
            aVal = new Date(aVal || 0);
            bVal = new Date(bVal || 0);
          }
          
          // Handle string fields
          if (typeof aVal === 'string') {
            aVal = aVal.toLowerCase();
            bVal = bVal.toLowerCase();
          }
          
          if (aVal < bVal) return direction === 'asc' ? -1 : 1;
          if (aVal > bVal) return direction === 'asc' ? 1 : -1;
          return 0;
        });
      }

      function handleSort(field) {
        const header = document.querySelector(`[data-sort="${field}"]`);
        
        // Remove sort classes from all headers
        document.querySelectorAll('.sortable').forEach(h => {
          h.classList.remove('sort-asc', 'sort-desc');
        });
        
        // Toggle sort direction
        if (currentSort.field === field) {
          currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
          currentSort.field = field;
          currentSort.direction = 'asc';
        }
        
        // Add sort class to current header
        header.classList.add(`sort-${currentSort.direction}`);
        
        // Re-sort and display
        sortTickets(filteredTickets, currentSort.field, currentSort.direction);
        displayMyTickets(filteredTickets);
        updatePagination();
      }

      function displayMyTickets(tickets) {
        const ticketsList = document.getElementById("my-tickets-list");
        if (!ticketsList) return;
        
        // Calculate pagination
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const pageTickets = tickets.slice(startIndex, endIndex);
        
        ticketsList.innerHTML = ""; // Clear the list
        
        if (pageTickets.length === 0) {
          ticketsList.innerHTML = "<tr><td colspan='8' class='text-center'>No tickets found</td></tr>";
          return;
        }
        
        pageTickets.forEach((ticket) => {
          const row = document.createElement("tr");
          
          // Determine user's role for this ticket
          const username = localStorage.getItem("username");
          let userRole = "";
          if (ticket.projectManager === username) {
            userRole = "Project Manager";
          } else if (ticket.assignedTo === username) {
            userRole = "Assigned To";
          } else if (Array.isArray(ticket.supportingTeam) && ticket.supportingTeam.includes(username)) {
            userRole = "Supporting Team";
          }
          
          // Format due date
          const dueDate = ticket.dueDate ? new Date(ticket.dueDate).toLocaleDateString() : "N/A";
          
          // Get status badge class
          const statusClass = getStatusBadgeClass(ticket.status);
          
          row.innerHTML = `
            <td>${ticket.ticketNumber || ''}</td>
            <td>${ticket.projectId || ''}</td>
            <td>${ticket.projectName || ''}</td>
            <td>${ticket.ticketHeading || ''}</td>
            <td>${dueDate}</td>
            <td><span class="badge badge-${statusClass}">${ticket.status || ''}</span></td>
            <td>${userRole}</td>
            <td>
              <button class="btn btn-link info-btn" onclick="showInstructionsModal(\`${(ticket.instructions || '').replace(/`/g, '\\`').replace(/'/g, '\\\'').replace(/"/g, '&quot;')}\`)" title="Instructions" data-toggle="tooltip">
                <i class="fas fa-info-circle"></i>
              </button>
              <a href="Tickets.html" class="btn btn-sm btn-primary">View Details</a>
            </td>
          `;
          
          ticketsList.appendChild(row);
        });
        
        // Update pagination info
        document.getElementById('showing-start').textContent = startIndex + 1;
        document.getElementById('showing-end').textContent = Math.min(endIndex, tickets.length);
        document.getElementById('total-tickets-count').textContent = tickets.length;
      }

      function showNotification(message, type = 'info') {
        const toast = document.getElementById('toast-notification');
        if (toast) {
          toast.textContent = message;
          if (type === 'success') {
            toast.style.backgroundColor = '#28a745';
          } else if (type === 'warning') {
            toast.style.backgroundColor = '#ffc107';
            toast.style.color = '#212529';
          } else {
            toast.style.backgroundColor = '#17a2b8';
            toast.style.color = 'white';
          }
          toast.classList.add('show');
          setTimeout(() => toast.classList.remove('show'), 3000);
        }
      }

      function updatePagination() {
        const totalPages = Math.ceil(filteredTickets.length / pageSize);
        const paginationContainer = document.getElementById('pagination-controls');
        
        if (!paginationContainer) return;
        
        let paginationHTML = '';
        
        // Previous button
        paginationHTML += `
          <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Previous</a>
          </li>
        `;
        
        // Page numbers
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);
        
        for (let i = startPage; i <= endPage; i++) {
          paginationHTML += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
              <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
          `;
        }
        
        // Next button
        paginationHTML += `
          <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Next</a>
          </li>
        `;
        
        paginationContainer.innerHTML = paginationHTML;
      }

      function changePage(page) {
        const totalPages = Math.ceil(filteredTickets.length / pageSize);
        if (page >= 1 && page <= totalPages) {
          currentPage = page;
          displayMyTickets(filteredTickets);
          updatePagination();
        }
      }

      function changePageSize() {
        const newPageSize = parseInt(document.getElementById('page-size-select').value);
        pageSize = newPageSize;
        currentPage = 1; // Reset to first page
        displayMyTickets(filteredTickets);
        updatePagination();
      }

      function getStatusBadgeClass(status) {
        switch (status?.toLowerCase()) {
          case 'open':
            return 'warning';
          case 'in-progress':
            return 'primary';
          case 'resolved':
            return 'success';
          default:
            return 'secondary';
        }
      }

      // Add event listeners for new features
      document.addEventListener("DOMContentLoaded", function() {
        // Existing event listeners
        loadProjects();
        loadMyTickets();
        
        const searchInput = document.getElementById('project-search');
        const clearSearchBtn = document.getElementById('clear-search');
        const categorySelect = document.getElementById('project-category');
        
        searchInput.addEventListener('input', filterProjects);
        clearSearchBtn.addEventListener('click', clearSearch);
        categorySelect.addEventListener('change', toggleCategoryView);

        // New event listeners for tickets
        const ticketSearchInput = document.getElementById('ticket-search-input');
        const statusFilterSelect = document.getElementById('status-filter-select');
        const roleFilterSelect = document.getElementById('role-filter-select');
        const pageSizeSelect = document.getElementById('page-size-select');

        if (ticketSearchInput) {
          ticketSearchInput.addEventListener('input', applyFiltersAndDisplay);
        }

        if (statusFilterSelect) {
          statusFilterSelect.addEventListener('change', applyFiltersAndDisplay);
        }

        if (roleFilterSelect) {
          roleFilterSelect.addEventListener('change', applyFiltersAndDisplay);
        }

        if (pageSizeSelect) {
          pageSizeSelect.addEventListener('change', changePageSize);
        }

        // Add click event listeners for sortable headers
        document.addEventListener('click', function(e) {
          if (e.target.closest('.sortable')) {
            const header = e.target.closest('.sortable');
            const field = header.getAttribute('data-sort');
            if (field) {
              e.preventDefault();
              handleSort(field);
            }
          }
        });
      });
    </script>
    <script>
      function showInstructionsModal(instructions) {
        document.getElementById('instructionsText').innerHTML = instructions || 'No instructions available';
        // For Bootstrap 5:
        if (window.bootstrap && bootstrap.Modal) {
          var modal = new bootstrap.Modal(document.getElementById('instructionsModal'));
          modal.show();
        } else {
          // For Bootstrap 4 fallback
          $('#instructionsModal').modal('show');
        }
      }
      </script>
    <!-- Instructions Modal for Bootstrap 4.5.2 -->
    <div class="modal fade" id="instructionsModal" tabindex="-1" role="dialog" aria-labelledby="instructionsModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="instructionsModalLabel">Instructions</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div id="instructionsText" class="formatted-content"></div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
    <!-- Modal for Coming Soon -->
    <div class="modal fade" id="comingSoonModal" tabindex="-1" role="dialog" aria-labelledby="comingSoonLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="comingSoonLabel">Coming Soon</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            The Estimates feature is coming soon!
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
      var estimatesLink = document.getElementById('estimates-link');
      if (estimatesLink) {
        var isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        if (isDev) {
          estimatesLink.setAttribute('href', 'Estimates.html');
          estimatesLink.onclick = null;
        } else {
          estimatesLink.setAttribute('href', '#');
          estimatesLink.onclick = function(e) {
            e.preventDefault();
            if (window.jQuery && window.jQuery.fn.modal) {
              $('#comingSoonModal').modal('show');
            } else {
              alert('The Estimates feature is coming soon!');
            }
          };
        }
      }
    });
    </script>
  </body>  
</html>




