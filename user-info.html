<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile</title>
    <!-- Bootstrap CSS -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles/style.css">
    <link rel="stylesheet" href="styles/user-info.css">
</head>
<body>
    <div class="container-fluid">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                
            </div>
            <ul class="nav-links">
                <li class="admin-only"><a href="/index.html"><i class="fas fa-home"></i> Dashboard</a></li>
                <li class="admin-only"><a href="/Tickets.html"><i class="fas fa-ticket-alt"></i> Tickets</a></li>
                <li class="admin-only"><a href="/Registration.html"><i class="fas fa-user-plus"></i> User Management</a></li>
                <li class="admin-only"><a href="/additionalsetting.html"><i class="fas fa-cogs"></i> Additional Settings</a></li>
                <li class="admin-only"><a href="/AuditLogs.html"><i class="fas fa-history"></i> Audit Logs</a></li>
            </ul>
        </nav>

        <!-- Main Content -->
        <div class="main-content" style="padding-top: 10px; overflow-y: auto; height: calc(100vh - 60px); margin-left: 250px;">
            <!-- Top Bar -->    
            <header class="top-bar-user-info">
                <button onclick="goHome()" class="home-button">
                    <i class="fas fa-home"></i> Home
                </button>
                <div class="user-info">
                    <div class="user-photo-container">
                        <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp" alt="User Photo" class="user-photo" onerror="this.onerror=null;this.src='https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';" />
                        <div class="user-dropdown-content">
                            <a href="/user-info.html" onclick="openSettings()"><i class="fas fa-cog"></i> Settings</a>
                        </div>
                    </div>
                    <div class="user-details">
                        <span class="user-name">Welcome, User</span>
                        <span class="user-role">Role</span>
                    </div>
                    <button onclick="logout()" class="logout-button">Logout</button>
                </div>
            </header>

            <!-- User Profile Content -->
            <div class="user-info-container">
                <div class="user-profile-card">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <img src="user-avatar.jpg" alt="User Avatar" id="userAvatar">
                            <div class="avatar-upload">
                                <i class="fas fa-camera"></i>
                                <input type="file" id="avatarInput" accept="image/*" style="display: none;">
                            </div>
                        </div>
                        <div class="profile-info">
                            <h1 class="profile-name" id="profileName">Loading...</h1>
                            <span class="profile-role" id="profileRole">Loading...</span>
                        </div>
                    </div>

                    <div class="profile-content">
                        <div class="info-section">
                            <h2 class="section-title">
                                <i class="fas fa-user"></i>
                                Personal Information
                            </h2>
                            <div class="info-item">
                                <div class="info-label">Email</div>
                                <div class="info-value" id="userEmail">Loading...</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Phone</div>
                                <div class="info-value" id="userPhone">Loading...</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Location</div>
                                <div class="info-value" id="userLocation">Loading...</div>
                            </div>
                        </div>

                        <div class="info-section">
                            <h2 class="section-title">
                                <i class="fas fa-briefcase"></i>
                                Work Information
                            </h2>
                            <div class="info-item">
                                <div class="info-label">Department</div>
                                <div class="info-value" id="userDepartment">Loading...</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Position</div>
                                <div class="info-value" id="userPosition">Loading...</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Join Date</div>
                                <div class="info-value" id="userJoinDate">Loading...</div>
                            </div>
                        </div>
                    </div>

                    <button class="edit-profile-btn" id="editProfileBtn">
                        <i class="fas fa-edit"></i>
                        Edit Profile
                    </button>

                    <div class="activity-log">
                        <h2 class="section-title">Recent Activity</h2>
                        <div id="activityContainer">
                            <!-- Activity items will be dynamically loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Profile Modal -->
    <div class="modal fade" id="editProfileModal" tabindex="-1" role="dialog" aria-labelledby="editProfileModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editProfileModalLabel">Edit Profile</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="editProfileForm">
                        <div class="form-group">
                            <label for="editPhone">Phone</label>
                            <input type="tel" class="form-control" id="editPhone" required>
                        </div>
                        <div class="form-group">
                            <label for="editLocation">Location</label>
                            <input type="text" class="form-control" id="editLocation" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveProfileBtn">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
        // Check authentication on page load
        document.addEventListener('DOMContentLoaded', async () => {
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/login.html';
                return;
            }

            // Initialize photos immediately with default or stored avatar
            const userAvatar = localStorage.getItem('userAvatar') || 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
            
            // Update top bar photo
            const topBarPhoto = document.querySelector('.user-info .user-photo');
            if (topBarPhoto) {
                topBarPhoto.src = userAvatar;
                topBarPhoto.onerror = () => {
                    topBarPhoto.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
                    localStorage.setItem('userAvatar', 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp');
                };
            }
            
            // Update profile avatar
            const profileAvatar = document.getElementById('userAvatar');
            if (profileAvatar) {
                profileAvatar.src = userAvatar;
                profileAvatar.onerror = () => {
                    profileAvatar.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
                    localStorage.setItem('userAvatar', 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp');
                };
            }

            // Load user data
            await loadUserProfile();
            await loadUserActivity();
            updateUIWithUserInfo();
        });

        // Fetch wrapper with authentication
        async function fetchWithAuth(url, options = {}) {
            const token = localStorage.getItem('token');
            const defaultOptions = {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            };
            
            return fetch(url, { ...defaultOptions, ...options });
        }

        // Load user profile data
        async function loadUserProfile() {
            try {
                const response = await fetchWithAuth('/api/user/profile');
                if (!response.ok) throw new Error('Failed to load profile');
                
                const userData = await response.json();
                
                // Update avatar if exists
                if (userData.avatarUrl) {
                    document.getElementById('userAvatar').src = userData.avatarUrl;
                }
                
                // Update other profile information
                document.getElementById('profileName').textContent = userData.username || 'N/A';
                document.getElementById('profileRole').textContent = userData.role || 'N/A';
                document.getElementById('userEmail').textContent = userData.email || 'N/A';
                document.getElementById('userPhone').textContent = userData.phone || 'N/A';
                document.getElementById('userLocation').textContent = userData.location || 'N/A';
                document.getElementById('userDepartment').textContent = userData.department || 'N/A';
                document.getElementById('userPosition').textContent = userData.position || 'N/A';
                document.getElementById('userJoinDate').textContent = 
                    userData.joinDate ? new Date(userData.joinDate).toLocaleDateString() : 'N/A';
            } catch (error) {
                console.error('Error loading profile:', error);
                alert('Failed to load profile information');
            }
        }

        // Load user activity
        async function loadUserActivity() {
            try {
                const response = await fetchWithAuth('/api/user/activity');
                if (!response.ok) throw new Error('Failed to load activity');
                const activities = await response.json();
                
                const activityContainer = document.getElementById('activityContainer');
                activityContainer.innerHTML = activities.map(activity => `
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas ${getActivityIcon(activity.type)}"></i>
                        </div>
                        <div class="activity-details">
                            <div class="activity-description">${activity.description}</div>
                            <div class="activity-time">${formatActivityTime(activity.timestamp)}</div>
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('Error loading activity:', error);
            }
        }

        // Update UI with user info from localStorage
        function updateUIWithUserInfo() {
            const userRole = localStorage.getItem('role');
            const userName = localStorage.getItem('username');
            const userAvatar = localStorage.getItem('userAvatar') || 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
            
            // Update text elements
            const userNameElement = document.querySelector('.user-name');
            const userRoleElement = document.querySelector('.user-role');
            if (userNameElement) userNameElement.textContent = `Welcome, ${userName || 'User'}`;
            if (userRoleElement) userRoleElement.textContent = userRole || 'Role';
            
            // Update images
            const topBarPhoto = document.querySelector('.user-info .user-photo');
            if (topBarPhoto) {
                topBarPhoto.src = userAvatar;
            }
            
            const profileAvatar = document.getElementById('userAvatar');
            if (profileAvatar) {
                profileAvatar.src = userAvatar;
            }
        }

        // Handle avatar upload
        document.querySelector('.avatar-upload').addEventListener('click', () => {
            document.getElementById('avatarInput').click();
        });

        document.getElementById('avatarInput').addEventListener('change', async (e) => {
            const file = e.target.files[0];
            if (file) {
                const formData = new FormData();
                formData.append('avatar', file);
                
                try {
                    const response = await fetchWithAuth('/api/user/avatar', {
                        method: 'POST',
                        body: formData
                    });
                    
                    if (!response.ok) {
                        throw new Error('Upload failed');
                    }

                    const data = await response.json();
                    
                    // Update avatar in localStorage
                    localStorage.setItem('userAvatar', data.avatarUrl);
                    
                    // Update both profile avatar and top bar photo
                    const userAvatar = document.getElementById('userAvatar');
                    const topBarPhoto = document.querySelector('.user-photo');
                    
                    if (userAvatar) userAvatar.src = data.avatarUrl;
                    if (topBarPhoto) topBarPhoto.src = data.avatarUrl;
                    
                    // Show success message
                    alert('Avatar uploaded successfully!');
                } catch (error) {
                    console.error('Error uploading avatar:', error);
                    alert('Failed to upload avatar. Please try again.');
                }
            }
        });

        // Handle profile editing
        document.getElementById('editProfileBtn').addEventListener('click', () => {
            document.getElementById('editPhone').value = document.getElementById('userPhone').textContent;
            document.getElementById('editLocation').value = document.getElementById('userLocation').textContent;
            $('#editProfileModal').modal('show');
        });

        document.getElementById('saveProfileBtn').addEventListener('click', async () => {
            const updatedData = {
                phone: document.getElementById('editPhone').value,
                location: document.getElementById('editLocation').value
            };

            try {
                const response = await fetchWithAuth('/api/user/profile', {
                    method: 'PUT',
                    body: JSON.stringify(updatedData)
                });

                if (response.ok) {
                    await loadUserProfile();
                    $('#editProfileModal').modal('hide');
                }
            } catch (error) {
                console.error('Error updating profile:', error);
            }
        });

        // Utility functions
        function getActivityIcon(type) {
            const icons = {
                'project': 'fa-project-diagram',
                'ticket': 'fa-ticket-alt',
                'profile': 'fa-user-edit',
                'default': 'fa-info-circle'
            };
            return icons[type] || icons.default;
        }

        function formatActivityTime(timestamp) {
            const date = new Date(timestamp);
            const now = new Date();
            const diffMinutes = Math.floor((now - date) / (1000 * 60));
            
            if (diffMinutes < 60) return `${diffMinutes} minutes ago`;
            if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)} hours ago`;
            return date.toLocaleDateString();
        }

        // Navigation functions
        function goHome() {
  window.location.href = "index.html";
}

        function logout() {
            // First update all images to default
            const defaultPhoto = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
            
            const topBarPhoto = document.querySelector('.user-info .user-photo');
            if (topBarPhoto) {
                topBarPhoto.src = defaultPhoto;
            }
            
            const profileAvatar = document.getElementById('userAvatar');
            if (profileAvatar) {
                profileAvatar.src = defaultPhoto;
            }
            
            // Clear localStorage
            const itemsToClear = ['token', 'username', 'role', 'userAvatar', 'userId'];
            itemsToClear.forEach(item => localStorage.removeItem(item));
            
            // Set a default avatar before redirecting
            localStorage.setItem('userAvatar', defaultPhoto);
            
            // Small delay to ensure images are updated
            setTimeout(() => {
                window.location.href = '/login.html';
            }, 100);
        }

        function updateUserInterface() {
            const userRole = localStorage.getItem('role');
            const userName = localStorage.getItem('username');
            const userAvatar = localStorage.getItem('userAvatar');
            
            // Update username and role
            const userNameElement = document.querySelector('.user-name');
            const userRoleElement = document.querySelector('.user-role');
            if (userNameElement) userNameElement.textContent = `Welcome, ${userName || 'User'}`;
            if (userRoleElement) userRoleElement.textContent = userRole || 'Role';
            
            // Update the top bar photo specifically
            const topBarPhoto = document.querySelector('.user-info .user-photo');
            if (topBarPhoto) {
                topBarPhoto.src = userAvatar || 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
                topBarPhoto.onerror = () => {
                    topBarPhoto.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
                };
            }
            
            // Update the profile avatar if it exists
            const profileAvatar = document.getElementById('userAvatar');
            if (profileAvatar) {
                profileAvatar.src = userAvatar || 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
                profileAvatar.onerror = () => {
                    profileAvatar.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
                };
            }
        }

        // Call updateUserInterface when the page loads
        document.addEventListener('DOMContentLoaded', updateUserInterface);

        // Optional: Refresh user interface periodically
        setInterval(updateUserInterface, 30000);

        document.addEventListener("DOMContentLoaded", function() {
    const userRole = localStorage.getItem("role");
    const adminElements = document.querySelectorAll('.admin-only');
    
    adminElements.forEach(element => {
        if (userRole === "Admin") {
            element.style.display = "block";
        } else {
            element.style.display = "none";
        }
    });
});
    </script>
</body>
</html>
