<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Additional Settings</title>
    <!-- Bootstrap CSS -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <!-- Main App Styles -->
    <link rel="stylesheet" href="styles/style.css">
    <link rel="stylesheet" href="styles/additional-settings.css">
</head>
<body>
    <div class="container-fluid">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header"></div>
            <ul class="nav-links">
                <li class="admin-only"><a href="/index.html"><i class="fas fa-home"></i> Dashboard</a></li>
                <li class="admin-only"><a href="/Tickets.html"><i class="fas fa-ticket-alt"></i> Tickets</a></li>
                <li class="admin-only"><a href="/Registration.html"><i class="fas fa-user-plus"></i> User Management</a></li>
                <li class="admin-only active"><a href="/additionalsetting.html"><i class="fas fa-cogs"></i> Additional Settings</a></li>
                <li class="admin-only"><a href="/AuditLogs.html"><i class="fas fa-history"></i> Audit Logs</a></li>
            </ul>
        </nav>
        <!-- Main Content -->
        <div class="main-content" style="padding-top: 60px; overflow-y: auto; height: calc(100vh - 60px);">
            <!-- Top Bar -->
            <header class="top-bar-user-info">
                <button onclick="goHome()" class="home-button">
                    <i class="fas fa-home"></i> Home
                </button>
                <div class="user-info">
                    <div class="user-photo-container">
                        <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp" alt="User Photo" class="user-photo" onerror="this.onerror=null;this.src='https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';" />
                        <div class="user-dropdown-content">
                            <a href="/user-info.html"><i class="fas fa-cog"></i> Settings</a>
                        </div>
                    </div>
                    <div class="user-details">
                        <span class="user-name">Welcome, User</span>
                        <span class="user-role">Role</span>
                    </div>
                    <button onclick="logout()" class="logout-button">Logout</button>
                </div>
            </header>
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4 class="mb-0">Role Management</h4>
                                <button class="btn btn-link p-0" id="toggleRoleMgmtBtn" onclick="toggleSection('roleMgmtBody', 'toggleRoleMgmtBtn')">
                                    <i class="fas fa-chevron-up"></i>
                                </button>
                            </div>
                            <div class="card-body" id="roleMgmtBody">
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="role-section mb-4">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="mb-0">Project Manager</h5>
                                                <button class="btn btn-primary btn-sm" onclick="addUserToRole('Project Manager')">
                                                    <i class="fas fa-plus"></i> Add User
                                                </button>
                                            </div>
                                            <div id="projectManagerList" class="role-users-list"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="role-section mb-4">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="mb-0">Creative Service</h5>
                                                <button class="btn btn-primary btn-sm" onclick="addUserToRole('Creative Service')">
                                                    <i class="fas fa-plus"></i> Add User
                                                </button>
                                            </div>
                                            <div id="creativeServiceList" class="role-users-list"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="role-section mb-4">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="mb-0">Quality</h5>
                                                <button class="btn btn-primary btn-sm" onclick="addUserToRole('Quality')">
                                                    <i class="fas fa-plus"></i> Add User
                                                </button>
                                            </div>
                                            <div id="qualityList" class="role-users-list"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="role-section mb-4">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="mb-0">Editorial</h5>
                                                <button class="btn btn-primary btn-sm" onclick="addUserToRole('Editorial')">
                                                    <i class="fas fa-plus"></i> Add User
                                                </button>
                                            </div>
                                            <div id="editorialList" class="role-users-list"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="role-section mb-4">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="mb-0">Creative Acquisition</h5>
                                                <button class="btn btn-primary btn-sm" onclick="addUserToRole('Creative Acquisition')">
                                                    <i class="fas fa-plus"></i> Add User
                                                </button>
                                            </div>
                                            <div id="creativeAcquisitionList" class="role-users-list"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="role-section mb-4">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="mb-0">Digital</h5>
                                                <button class="btn btn-primary btn-sm" onclick="addUserToRole('Digital')">
                                                    <i class="fas fa-plus"></i> Add User
                                                </button>
                                            </div>
                                            <div id="digitalList" class="role-users-list"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Client Management Card -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Client Management</h4>
                    <button class="btn btn-link p-0" id="toggleClientMgmtBtn" onclick="toggleSection('clientMgmtBody', 'toggleClientMgmtBtn')">
                        <i class="fas fa-chevron-up"></i>
                    </button>
                </div>
                <div class="card-body" id="clientMgmtBody">
                    <form id="clientForm" class="form-inline mb-3">
                        <div class="form-group mr-2">
                            <label for="clientName" class="mr-2">Client Name</label>
                            <input type="text" class="form-control" id="clientName" required>
                        </div>
                        <div class="form-group mr-2">
                            <label for="clientCode" class="mr-2">Client Code</label>
                            <input type="text" class="form-control" id="clientCode" required>
                        </div>
                        <button type="submit" class="btn btn-primary">Add Client</button>
                    </form>
                    <div class="table-responsive">
                        <table class="table table-bordered" id="clientTable">
                            <thead>
                                <tr>
                                    <th>Client Name</th>
                                    <th>Client Code</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Client rows will be inserted here by JS -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1" role="dialog" aria-labelledby="addUserModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addUserModalLabel">Add User to Role</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <input type="hidden" id="selectedRole">
                        <div class="form-group">
                            <label for="userSelect">Select User</label>
                            <select class="form-control" id="userSelect" required>
                                <option value="">Select a user...</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="saveUserToRole()">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Client Edit Modal -->
    <div class="modal fade" id="editClientModal" tabindex="-1" role="dialog" aria-labelledby="editClientModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editClientModalLabel">Edit Client</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="editClientForm">
                        <input type="hidden" id="editClientId">
                        <div class="form-group">
                            <label for="editClientName">Client Name</label>
                            <input type="text" class="form-control" id="editClientName" required>
                        </div>
                        <div class="form-group">
                            <label for="editClientCode">Client Code</label>
                            <input type="text" class="form-control" id="editClientCode" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="saveClientChanges">Save changes</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS (should already be included) -->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        const token = localStorage.getItem('token');

        const roleIdMap = {
            'Project Manager': 'projectManagerList',
            'Creative Service': 'creativeServiceList',
            'Quality': 'qualityList',
            'Editorial': 'editorialList',
            'Creative Acquisition': 'creativeAcquisitionList',
            'Digital': 'digitalList'
        };

        // Function to load users for each role
        async function loadRoleUsers() {
            try {
                const response = await fetch('/api/role-users', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });
                if (!response.ok) {
                    alert("Session expired or unauthorized. Please log in again.");
                    window.location.href = "/login.html";
                    return;
                }
                const data = await response.json();
                
                // Update each role's user list
                updateRoleUserList('Project Manager', data.projectManager);
                updateRoleUserList('Creative Service', data.creativeService);
                updateRoleUserList('Quality', data.quality);
                updateRoleUserList('Editorial', data.editorial);
                updateRoleUserList('Creative Acquisition', data.creativeAcquisition);
                updateRoleUserList('Digital', data.digital);
            } catch (error) {
                console.error('Error loading role users:', error);
                showAlert('Error loading role users', 'danger');
            }
        }

        // Function to update a role's user list
        function updateRoleUserList(role, users) {
            const listId = roleIdMap[role];
            const listElement = document.getElementById(listId);
            listElement.innerHTML = '';

            if (!Array.isArray(users)) {
                console.error("Users response is not an array:", users);
                return;
            }

            users.forEach(user => {
                const userElement = document.createElement('div');
                userElement.className = 'role-user-item d-flex justify-content-between align-items-center p-2 border-bottom';
                userElement.innerHTML = `
                    <span>${user.username || user.name || user.email}</span>
                    <button class="btn btn-danger btn-sm" onclick="removeUserFromRole('${role}', '${user._id}')">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                listElement.appendChild(userElement);
            });
        }

        // Function to add user to role
        async function addUserToRole(role) {
            document.getElementById('selectedRole').value = role;
            document.getElementById('addUserModalLabel').textContent = `Add User to ${role}`;
            
            try {
                const response = await fetch('/api/users', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });
                if (!response.ok) {
                    alert("Session expired or unauthorized. Please log in again.");
                    window.location.href = "/login.html";
                    return;
                }
                const users = await response.json();
                
                const userSelect = document.getElementById('userSelect');
                userSelect.innerHTML = '<option value="">Select a user...</option>';
                
                if (!Array.isArray(users)) {
                    console.error("Users response is not an array:", users);
                    return;
                }
                
                users.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user._id;
                    option.textContent = user.username;
                    userSelect.appendChild(option);
                });
                
                $('#addUserModal').modal('show');
            } catch (error) {
                console.error('Error loading users:', error);
                showAlert('Error loading users', 'danger');
            }
        }

        // Function to save user to role
        async function saveUserToRole() {
            const role = document.getElementById('selectedRole').value;
            const userId = document.getElementById('userSelect').value;
            
            if (!userId) {
                showAlert('Please select a user', 'warning');
                return;
            }
            
            try {
                const response = await fetch('/api/role-users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + token
                    },
                    body: JSON.stringify({ role, userId })
                });
                
                if (response.ok) {
                    $('#addUserModal').modal('hide');
                    loadRoleUsers();
                    showAlert('User added successfully', 'success');
                } else {
                    throw new Error('Failed to add user');
                }
            } catch (error) {
                console.error('Error adding user to role:', error);
                showAlert('Error adding user to role', 'danger');
            }
        }

        // Function to remove user from role
        async function removeUserFromRole(role, userId) {
            if (!confirm('Are you sure you want to remove this user from the role?')) {
                return;
            }
            
            try {
                const response = await fetch('/api/role-users', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + token
                    },
                    body: JSON.stringify({ role, userId })
                });
                
                if (response.ok) {
                    loadRoleUsers();
                    showAlert('User removed successfully', 'success');
                } else {
                    throw new Error('Failed to remove user');
                }
            } catch (error) {
                console.error('Error removing user from role:', error);
                showAlert('Error removing user from role', 'danger');
            }
        }

        // Load role users when page loads
        document.addEventListener('DOMContentLoaded', loadRoleUsers);

        function showAlert(message, type = 'info') {
            // You can use Bootstrap alerts or a simple alert for now
            alert(message);
        }

        // --- Client Management JS ---
        async function loadClients() {
            try {
                const token = localStorage.getItem("token");
                if (!token) {
                    alert("You are not logged in. Please log in again.");
                    window.location.href = "/login.html";
                    return;
                }
                const response = await fetch('/api/clients', {
                    headers: {
                        Authorization: `Bearer ${token}`
                    }
                });
                if (!response.ok) {
                    throw new Error("Failed to fetch clients");
                }
                const clients = await response.json();
                const tbody = document.querySelector('#clientTable tbody');
                tbody.innerHTML = '';
                if (!Array.isArray(clients)) {
                    console.error("Clients response is not an array:", clients);
                    return;
                }
                clients.forEach(client => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${client.clientName}</td>
                        <td>${client.clientCode}</td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="showEditClientModal('${client._id}', '${client.clientName}', '${client.clientCode}')"><i class="fas fa-edit"></i></button>
                            <button class="btn btn-sm btn-danger" onclick="deleteClient('${client._id}')"><i class="fas fa-trash"></i></button>
                        </td>
                    `;
                    tbody.appendChild(tr);
                });
            } catch (error) {
                console.error('Error loading clients:', error);
                showAlert('Error loading clients', 'danger');
            }
        }

        document.getElementById('clientForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            const clientName = document.getElementById('clientName').value.trim();
            const clientCode = document.getElementById('clientCode').value.trim();
            if (!clientName || !clientCode) return showAlert('Both fields are required', 'warning');
            try {
                const token = localStorage.getItem("token");
                const response = await fetch('/api/clients', {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        Authorization: `Bearer ${token}`
                    },
                    body: JSON.stringify({ clientName, clientCode })
                });
                if (response.ok) {
                    document.getElementById('clientForm').reset();
                    loadClients();
                    showAlert('Client added successfully', 'success');
                } else {
                    const data = await response.json();
                    showAlert(data.error || 'Failed to add client', 'danger');
                }
            } catch (error) {
                showAlert('Error adding client', 'danger');
            }
        });

        function showEditClientModal(id, name, code) {
            document.getElementById('editClientId').value = id;
            document.getElementById('editClientName').value = name;
            document.getElementById('editClientCode').value = code;
            $('#editClientModal').modal('show');
        }

        document.getElementById('saveClientChanges').addEventListener('click', async function() {
            const id = document.getElementById('editClientId').value;
            const clientName = document.getElementById('editClientName').value.trim();
            const clientCode = document.getElementById('editClientCode').value.trim();
            if (!clientName || !clientCode) return showAlert('Both fields are required', 'warning');
            try {
                const token = localStorage.getItem("token");
                const response = await fetch(`/api/clients/${id}`, {
                    method: 'PUT',
                    headers: { 
                        'Content-Type': 'application/json',
                        Authorization: `Bearer ${token}`
                    },
                    body: JSON.stringify({ clientName, clientCode })
                });
                if (response.ok) {
                    $('#editClientModal').modal('hide');
                    loadClients();
                    showAlert('Client updated successfully', 'success');
                } else {
                    const data = await response.json();
                    showAlert(data.error || 'Failed to update client', 'danger');
                }
            } catch (error) {
                showAlert('Error updating client', 'danger');
            }
        });

        async function deleteClient(id) {
            if (!confirm('Are you sure you want to delete this client?')) return;
            try {
                const token = localStorage.getItem("token");
                const response = await fetch(`/api/clients/${id}`, { 
                    method: 'DELETE',
                    headers: {
                        Authorization: `Bearer ${token}`
                    }
                });
                if (response.ok) {
                    loadClients();
                    showAlert('Client deleted successfully', 'success');
                } else {
                    const data = await response.json();
                    showAlert(data.error || 'Failed to delete client', 'danger');
                }
            } catch (error) {
                showAlert('Error deleting client', 'danger');
            }
        }

        // Load clients when page loads
        document.addEventListener('DOMContentLoaded', loadClients);

        // --- Topbar User Info JS ---
        function updateTopbarUserInfo() {
            const userName = localStorage.getItem('username') || 'User';
            const userRole = localStorage.getItem('role') || 'Role';
            const userAvatar = localStorage.getItem('userAvatar') || 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
            const userNameElement = document.querySelector('.user-name');
            const userRoleElement = document.querySelector('.user-role');
            const userPhoto = document.querySelector('.user-photo');
            if (userNameElement) userNameElement.textContent = `Welcome, ${userName}`;
            if (userRoleElement) userRoleElement.textContent = userRole;
            if (userPhoto) {
                userPhoto.src = userAvatar;
                userPhoto.onerror = () => { userPhoto.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'; };
            }
        }
        function goHome() {
            window.location.href = '/index.html';
        }
        function logout() {
            // Clear user info and redirect to login
            localStorage.removeItem('token');
            localStorage.removeItem('username');
            localStorage.removeItem('role');
            localStorage.removeItem('userAvatar');
            window.location.href = '/login.html';
        }
        document.addEventListener('DOMContentLoaded', updateTopbarUserInfo);

        function toggleSection(bodyId, btnId) {
            const body = document.getElementById(bodyId);
            const btn = document.getElementById(btnId).querySelector('i');
            if (body.style.display === 'none') {
                body.style.display = '';
                btn.classList.remove('fa-chevron-down');
                btn.classList.add('fa-chevron-up');
            } else {
                body.style.display = 'none';
                btn.classList.remove('fa-chevron-up');
                btn.classList.add('fa-chevron-down');
            }
        }
    </script>
</body>
</html> 