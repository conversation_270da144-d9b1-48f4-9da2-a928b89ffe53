<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Project Details</title>
  <link rel="stylesheet" href="styles/style.css">
  <link rel="stylesheet" href="styles/styles.css">
  <link rel="stylesheet" href="styles/project-details.css">
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
  <button class="sidebar-toggle" id="sidebarToggle">
    <i class="fas fa-bars"></i>
  </button>
  <div class="container-fluid">
    <div class="row" style="margin: 0; width: 100%;">
      <div class="sidebar">
        <div class="dashboard-header">
          <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1747737528/logo_i76d4f.jpg" alt="Logo" class="sidebar-logo">
          <h2>Dashboards</h2>
        </div>
        <hr class="divider" />
        <nav>
          <h3>PROJECT LIST</h3>
          <div class="sidebar-search">
            <div class="input-group">
              <input type="text" id="project-search" class="form-control form-control-sm" placeholder="Search by Project ID...">
              <div class="input-group-append">
                <button class="btn btn-sm btn-outline-secondary" type="button" id="clear-search">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </div>
          <div class="sidebar-category mt-3 mb-2">
            <select id="project-category" class="form-control form-control-sm">
              <option value="all">All Projects</option>
              <option value="client">By Client</option>
            </select>
          </div>
          <div id="project-list-container">
            <ul id="project-list">
              <!-- Project items will be dynamically loaded here -->
            </ul>
          </div>
          <div id="client-category-container" style="display: none;">
            <div id="client-accordion" class="accordion">
              <!-- Client categories will be dynamically loaded here -->
            </div>
          </div>
        </nav>
      </div>
      <div class="main-wrapper" style="padding-top: 60px; overflow-y: auto; height: calc(100vh - 60px);">
        <header class="top-bar">
          <div class="left-section">
            <button onclick="goHome()" class="home-button">
              <i class="fas fa-home"></i> <span class="home-text">Home</span>
            </button>
            <div class="dropdown-container">
              <div class="dropdown">
                <button class="dropdown-button">Production</button>
                <div class="dropdown-content">
                  <a href="Move-to-Production.html">Move to Production</a>
                </div>
              </div>
              <div class="dropdown">
                <button class="dropdown-button">Reports</button>
                <div class="dropdown-content">
                  <a href="WIP-report.html">WIP Report</a>
                </div>
              </div>
              <div class="dropdown">
                <button class="dropdown-button">Project Management</button>
                <div class="dropdown-content">
                  <a href="Invoices.html">Invoice</a>
                </div>
              </div>
              <div class="dropdown">
                <button class="dropdown-button">Tickets</button>
                <div class="dropdown-content">
                  <a href="Tickets.html">Tickets</a>
                </div>
              </div>
            </div>
          </div>
          <div class="right-section">
            <div class="user-info">
              <div class="user-photo-container">
                <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp" alt="User Photo" class="user-photo" id="userAvatar" onerror="this.onerror=null;this.src='https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';">
                <div class="user-dropdown-content">
                  <a href="/user-info.html"><i class="fas fa-cog"></i> Settings</a>
                  <a href="/AuditLogs.html" class="admin-only"><i class="fas fa-history"></i> Audit Logs</a>
                </div>
              </div>
              <div class="user-details">
                <span class="user-name" id="username">Welcome, User</span>
                <span class="user-role" id="userRole">Role</span>
              </div>
            </div>
            <button onclick="logout()" class="logout-button">Logout</button>
          </div>
        </header>
        <div class="main-content" id="main-content">
          <div class="dashboard-column-layout">
            <div class="dashboard-main-column">
              <div class="project-details-container">
                <div class="project-details-header">
                  <h2>Project Details</h2>
                  <div class="project-details-icons" id="project-details-icons">
                    <!-- Buttons will be injected by JS based on permissions -->
                  </div>
                </div>
                <div id="project-details-content" class="project-details-content">
                  <!-- Project details will be loaded here -->
                </div>
                <div id="move-to-completed-container" style="margin-top: 20px; display: none;">
                  <button class="btn btn-success" id="markCompletedBtn" style="background-color: green; border: none;">
                    Mark as Completed
                  </button>
                </div>
              </div>
            </div>
            <div class="dashboard-side-column">
              <div class="project-links-container">
                <div class="project-links-header">
                  <h2>Project Links</h2>
                  <div class="project-links-controls">
                    <button class="btn btn-sm btn-secondary" id="toggleEditModeBtn" title="Toggle Edit Mode" style="display: none;">
                      <i class="fas fa-edit"></i> Edit Mode
                    </button>
                    <button class="btn btn-sm btn-primary" id="addMainBucketBtn" title="Add New Category">
                      <i class="fas fa-plus"></i> Add Category
                    </button>
                  </div>
                </div>
                <div id="project-links-content" class="project-links-content">
                  <!-- Project links will be loaded here -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    function goHome() {
      window.location.href = 'index.html';
    }
    function logout() {
      // Clear all user-related data
      const itemsToClear = ['token', 'username', 'role', 'userAvatar', 'userId'];
      itemsToClear.forEach(item => localStorage.removeItem(item));
      // Reset ALL user photos to default before redirecting
      const userPhotos = document.querySelectorAll('.user-photo, #userAvatar');
      userPhotos.forEach(photo => {
        photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
      });
      window.location.href = '/login.html';
    }
    // Sidebar and topbar user info update
    function updateUserInterface() {
      const userRole = localStorage.getItem('role');
      const userName = localStorage.getItem('username');
      const userAvatar = localStorage.getItem('userAvatar');
      const userNameElement = document.querySelector('.user-name');
      const userRoleElement = document.querySelector('.user-role');
      if (userNameElement) userNameElement.textContent = `Welcome, ${userName || 'User'}`;
      if (userRoleElement) userRoleElement.textContent = userRole || 'Role';
      const userPhotos = document.querySelectorAll('.user-photo');
      userPhotos.forEach(photo => {
        if (userAvatar) {
          photo.src = userAvatar;
        } else {
          photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
        }
      });
    }
    document.addEventListener('DOMContentLoaded', updateUserInterface);
    setInterval(updateUserInterface, 30000);
    // Project list and sidebar logic (copied from index.html)
    let allProjects = [];
    document.addEventListener("DOMContentLoaded", function() {
      loadProjects();
      const searchInput = document.getElementById('project-search');
      const clearSearchBtn = document.getElementById('clear-search');
      const categorySelect = document.getElementById('project-category');
      searchInput.addEventListener('input', filterProjects);
      clearSearchBtn.addEventListener('click', clearSearch);
      categorySelect.addEventListener('change', toggleCategoryView);
    });
    async function loadProjects() {
      const projectList = document.getElementById("project-list");
      projectList.innerHTML = "<li>Loading projects...</li>";
      try {
        const token = localStorage.getItem("token");
        const userRole = localStorage.getItem("role");
        const username = localStorage.getItem("username");
        const response = await fetch("/api/inprogressprojects", {
          headers: { Authorization: `Bearer ${token}` },
        });
        if (!response.ok) throw new Error(`Failed to fetch project details: ${response.statusText}`);
        const projects = await response.json();
        let filteredProjects = projects;
        if (userRole !== "Admin") {
          filteredProjects = projects.filter(project => {
            return (
              project.projectManager === username || 
              project.creativeServiceLead === username || 
              project.creativeAcquisitionLead === username ||
              (Array.isArray(project.supportingTeam) && project.supportingTeam.includes(username))
            );
          });
        }
        allProjects = filteredProjects;
        if (filteredProjects.length === 0) {
          projectList.innerHTML = "<li>No projects found</li>";
          return;
        }
        displayProjectsList(filteredProjects);
        prepareClientCategoryView(filteredProjects);
      } catch (error) {
        console.error("Error loading projects:", error);
        projectList.innerHTML = "<li>Error loading projects</li>";
      }
    }
    function displayProjectsList(projects) {
      const projectList = document.getElementById("project-list");
      projectList.innerHTML = "";
      projects.forEach((project) => {
        const li = document.createElement("li");
        const a = document.createElement("a");
        a.href = `Project-details.html?projectId=${project._id}`;
        a.textContent = `${project._id} - ${project.title}`;
        a.title = `${project._id} - ${project.title}`;
        li.appendChild(a);
        projectList.appendChild(li);
      });
    }
    function prepareClientCategoryView(projects) {
      const clientAccordion = document.getElementById("client-accordion");
      clientAccordion.innerHTML = "";
      const clientGroups = {};
      projects.forEach(project => {
        const clientName = project.clientName || "Unassigned";
        if (!clientGroups[clientName]) {
          clientGroups[clientName] = [];
        }
        clientGroups[clientName].push(project);
      });
      const sortedClients = Object.keys(clientGroups).sort();
      const clientList = document.createElement('ul');
      clientList.id = 'client-list';
      sortedClients.forEach(client => {
        const clientProjects = clientGroups[client];
        const projectCount = clientProjects.length;
        const li = document.createElement('li');
        const a = document.createElement('a');
        a.href = "#";
        a.innerHTML = `${client} <span class="project-count">${projectCount}</span>`;
        a.setAttribute("data-client", client);
        a.setAttribute("onclick", `toggleClientProjects('${client}')`);
        li.appendChild(a);
        const projectsList = document.createElement('ul');
        projectsList.className = 'client-projects';
        projectsList.id = `client-projects-${client.replace(/\s+/g, '').replace(/[^a-zA-Z0-9]/g, '')}`;
        projectsList.style.display = 'none';
        clientProjects.forEach(project => {
          const projectLi = document.createElement('li');
          projectLi.className = 'client-project-item';
          const projectA = document.createElement('a');
          projectA.href = `Project-details.html?projectId=${project._id}`;
          projectA.textContent = `${project._id} - ${project.title}`;
          projectA.title = `${project._id} - ${project.title}`;
          projectLi.appendChild(projectA);
          projectsList.appendChild(projectLi);
        });
        li.appendChild(projectsList);
        clientList.appendChild(li);
      });
      clientAccordion.appendChild(clientList);
    }
    function toggleClientProjects(client) {
      const clientId = client.replace(/\s+/g, '').replace(/[^a-zA-Z0-9]/g, '');
      const projectsList = document.getElementById(`client-projects-${clientId}`);
      if (projectsList.style.display === 'none') {
        document.querySelectorAll('.client-projects').forEach(list => {
          list.style.display = 'none';
        });
        projectsList.style.display = 'block';
        document.querySelectorAll('#client-list > li > a').forEach(a => {
          a.classList.remove('active');
        });
        document.querySelector(`#client-list a[data-client="${client}"]`).classList.add('active');
      } else {
        projectsList.style.display = 'none';
        document.querySelector(`#client-list a[data-client="${client}"]`).classList.remove('active');
      }
    }
    function filterProjects() {
      const searchTerm = document.getElementById('project-search').value.toLowerCase();
      const categoryView = document.getElementById('project-category').value;
      if (searchTerm === '') {
        if (categoryView === 'all') {
          displayProjectsList(allProjects);
        } else {
          prepareClientCategoryView(allProjects);
        }
        return;
      }
      const filteredProjects = allProjects.filter(project => 
        project._id.toLowerCase().includes(searchTerm)
      );
      if (categoryView === 'all') {
        displayProjectsList(filteredProjects);
      } else {
        prepareClientCategoryView(filteredProjects);
      }
    }
    function clearSearch() {
      document.getElementById('project-search').value = '';
      filterProjects();
    }
    function toggleCategoryView() {
      const categoryView = document.getElementById('project-category').value;
      const projectListContainer = document.getElementById('project-list-container');
      const clientCategoryContainer = document.getElementById('client-category-container');
      if (categoryView === 'all') {
        projectListContainer.style.display = 'block';
        clientCategoryContainer.style.display = 'none';
      } else {
        projectListContainer.style.display = 'none';
        clientCategoryContainer.style.display = 'block';
      }
      filterProjects();
    }
    // Project details fetch and display
    document.addEventListener('DOMContentLoaded', async function() {
      const urlParams = new URLSearchParams(window.location.search);
      const projectId = urlParams.get('projectId');
      if (!projectId) {
        document.getElementById('project-details-content').innerHTML = '<div class="alert alert-danger">No project selected.</div>';
        return;
      }
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/inprogressprojects/${projectId}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        if (!response.ok) throw new Error('Failed to fetch project details');
        const project = await response.json();
        const supportingTeamDisplay = project.supportingTeam && project.supportingTeam.length > 0 
          ? project.supportingTeam.join(', ') 
          : "None";
        let isbnDisplay = "None";
        if (project.isbns && project.isbns.length > 0) {
          isbnDisplay = project.isbns.join('<br>');
        } else if (project.isbn) {
          isbnDisplay = project.isbn;
        }
        let notesDisplay = 'No notes available';
        if (project.notes && project.notes.trim()) {
          if (project.notes.length > 150) {
            const truncatedNotes = project.notes.substring(0, 150) + '...';
            notesDisplay = `
              <div class="notes-content">
                <div class="notes-preview">${truncatedNotes}</div>
                <div class="notes-full" style="display: none;">${project.notes}</div>
                <button class="btn btn-sm btn-link notes-toggle" onclick="toggleNotes(this)">See More</button>
              </div>
            `;
          } else {
            notesDisplay = project.notes;
          }
        }
        const digitalProjectDisplay = project.isDigitalProject ? 'Yes' : 'No';
        document.getElementById('project-details-content').innerHTML = `
          <div class="project-details-box">
            <h3>Project ID</h3>
            <p>${project._id}</p>
          </div>
          <div class="project-details-box">
            <h3>Title</h3>
            <p>${project.title}</p>
          </div>
          <div class="project-details-box">
            <h3>Client Name</h3>
            <p>${project.clientName}</p>
          </div>
          <div class="project-details-box">
            <h3>Start Date</h3>
            <p>${new Date(project.startDate).toLocaleDateString()}</p>
          </div>
          <div class="project-details-box">
            <h3>End Date</h3>
            <p>${new Date(project.endDate).toLocaleDateString()}</p>
          </div>
          <div class="project-details-box">
            <h3>Project Manager</h3>
            <p>${project.projectManager}</p>
          </div>
          <div class="project-details-box">
            <h3>CS Lead</h3>
            <p>${project.creativeServiceLead}</p>
          </div>
          <div class="project-details-box">
            <h3>CA Lead</h3>
            <p>${project.creativeAcquisitionLead}</p>
          </div>
          <div class="project-details-box">
            <h3>Editorial Lead</h3>
            <p>${project.editorialLead || ''}</p>
          </div>
          <div class="project-details-box">
            <h3>QC Lead</h3>
            <p>${project.qcLead || ''}</p>
          </div>
          <div class="project-details-box">
            <h3>Digital Project</h3>
            <p>${digitalProjectDisplay}</p>
          </div>
          <div class="project-details-box">
            <h3>Digital Lead</h3>
            <p>${project.digitalLead || ''}</p>
          </div>
          <div class="project-details-box">
            <h3>Page Count</h3>
            <p>${project.pageCount}</p>
          </div>
          <div class="project-details-box">
            <h3>ISBN</h3>
            <p>${isbnDisplay}</p>
          </div>
          <div class="project-details-box">
            <h3>Trim Size</h3>
            <p>${project.trimSize}</p>
          </div>
          <div class="project-details-box">
            <h3>Color</h3>
            <p>${project.color}</p>
          </div>
          <div class="project-details-box">
            <h3>Levels</h3>
            <p>${project.levels}</p>
          </div>
          <div class="project-details-box">
            <h3>Units</h3>
            <p>${project.units}</p>
          </div>
          <div class="project-details-box">
            <h3>Parent Project ID</h3>
            <p>${project.parentProjectId || 'Same as Project ID'}</p>
          </div>
          <div class="project-details-box">
            <h3>Supporting Team</h3>
            <p>${supportingTeamDisplay}</p>
          </div>
          <div class="project-details-box">
            <h3>Notes</h3>
            <div class="notes-container">${notesDisplay}</div>
          </div>
        `;
        // Permission logic
        const userRole = localStorage.getItem('role');
        const userName = localStorage.getItem('username');
        const isAdmin = userRole === 'Admin';
        const isProjectManager = userName && project.projectManager && userName === project.projectManager;
        const iconsDiv = document.getElementById('project-details-icons');
        iconsDiv.innerHTML = '';
        if (isAdmin) {
          // Edit button
          const editBtn = document.createElement('button');
          editBtn.className = 'btn btn-info edit-button';
          editBtn.style.backgroundColor = 'blueviolet';
          editBtn.style.border = 'none';
          editBtn.id = 'editProjectBtn';
          editBtn.textContent = 'Edit';
          editBtn.onclick = function() {
            window.location.href = `editproject.html?projectId=${projectId}`;
          };
          iconsDiv.appendChild(editBtn);
          // Delete button
          const deleteBtn = document.createElement('button');
          deleteBtn.className = 'btn btn-info delete-button';
          deleteBtn.style.backgroundColor = 'red';
          deleteBtn.style.border = 'none';
          deleteBtn.id = 'deleteProjectBtn';
          deleteBtn.textContent = 'Delete';
          deleteBtn.onclick = async function() {
            if (!confirm('Are you sure you want to delete this project?')) return;
            const token = localStorage.getItem('token');
            const response = await fetch(`/api/inprogressprojects/${projectId}`, {
              method: 'DELETE',
              headers: { Authorization: `Bearer ${token}` }
            });
            if (response.ok) {
              alert('Project deleted successfully');
              window.location.href = 'index.html';
            } else {
              alert('Failed to delete project');
            }
          };
          iconsDiv.appendChild(deleteBtn);
          // Mark as Completed button (show at bottom)
          document.getElementById('move-to-completed-container').style.display = 'block';
        } else if (isProjectManager) {
          // Only Edit and Mark as Completed for project manager
          const editBtn = document.createElement('button');
          editBtn.className = 'btn btn-info edit-button';
          editBtn.style.backgroundColor = 'blueviolet';
          editBtn.style.border = 'none';
          editBtn.id = 'editProjectBtn';
          editBtn.textContent = 'Edit';
          editBtn.onclick = function() {
            window.location.href = `editproject.html?projectId=${projectId}`;
          };
          iconsDiv.appendChild(editBtn);
          document.getElementById('move-to-completed-container').style.display = 'block';
        }
        // View Status is always visible
        const viewStatusBtn = document.createElement('button');
        viewStatusBtn.className = 'btn btn-info';
        viewStatusBtn.id = 'viewStatusBtn';
        viewStatusBtn.textContent = 'View Status';
        viewStatusBtn.onclick = function() {
          window.location.href = `project-status.html?projectId=${projectId}`;
        };
        iconsDiv.appendChild(viewStatusBtn);

        // Export PDF icon button
        const exportPdfBtn = document.createElement('button');
        exportPdfBtn.className = 'btn btn-danger';
        exportPdfBtn.id = 'exportPdfBtn';
        exportPdfBtn.title = 'Export Project Details as PDF';
        exportPdfBtn.innerHTML = '<i class="fas fa-file-pdf"></i>';
        exportPdfBtn.style.marginLeft = '8px';
        exportPdfBtn.onclick = function() {
          exportProjectDetailsToPDF();
        };
        iconsDiv.appendChild(exportPdfBtn);
        // Mark as Completed handler (move to completed)
        document.getElementById('markCompletedBtn').onclick = async function() {
          const token = localStorage.getItem('token');
          const response = await fetch('/api/move-to-completed', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ projectId })
          });
          if (response.ok) {
            alert('Project marked as completed');
            window.location.reload();
          } else {
            alert('Failed to mark project as completed');
          }
        };
      } catch (error) {
        document.getElementById('project-details-content').innerHTML = `<div class="alert alert-danger">Failed to load project details.</div>`;
      }
    });
    function toggleNotes(btn) {
      const notesContent = btn.closest('.notes-content');
      const preview = notesContent.querySelector('.notes-preview');
      const full = notesContent.querySelector('.notes-full');
      if (full.style.display === 'none') {
        full.style.display = 'block';
        preview.style.display = 'none';
        btn.textContent = 'See Less';
      } else {
        full.style.display = 'none';
        preview.style.display = 'block';
        btn.textContent = 'See More';
      }
    }

    // Add jsPDF CDN if not present
    if (!document.getElementById('jspdf-cdn')) {
      const script = document.createElement('script');
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
      script.id = 'jspdf-cdn';
      document.head.appendChild(script);
    }

    // Add jsPDF autoTable CDN if not present
    if (!document.getElementById('jspdf-autotable-cdn')) {
      const script = document.createElement('script');
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.8.2/jspdf.plugin.autotable.min.js';
      script.id = 'jspdf-autotable-cdn';
      document.head.appendChild(script);
    }

    // Export function
    window.exportProjectDetailsToPDF = function() {
      function doExport() {
        const { jsPDF } = window.jspdf || window.jspdf || {};
        if (!jsPDF) {
          alert('PDF library not loaded. Please try again.');
          return;
        }
        const doc = new jsPDF();
        // Define margins
        const marginLeft = 15;
        const marginRight = 15;
        const pageWidth = doc.internal.pageSize.getWidth();
        const usableWidth = pageWidth - marginLeft - marginRight;
        doc.setFontSize(18);
        doc.setFont(undefined, 'bold');
        doc.text('Project Details', (pageWidth - doc.getTextWidth('Project Details')) / 2, 15);
        doc.setFont(undefined, 'normal');
        doc.setFontSize(12);
        let y = 25;
        let details = [];
        let projectId = '';
        let projectTitle = '';
        const excludeLabels = ['Levels', 'Units', 'Parent Project ID', 'Supporting Team'];
        const labelMap = {
          'Creative Service Lead': 'CS Lead',
          'Creative Acquisition Lead': 'CA Lead'
        };
        const content = document.getElementById('project-details-content');
        const boxes = content.querySelectorAll('.project-details-box');
        let notesValue = '';
        boxes.forEach(box => {
          let label = box.querySelector('h3')?.innerText || '';
          if (labelMap[label]) label = labelMap[label];
          if (excludeLabels.includes(label)) return;
          let value = '';
          if (label === 'Notes') {
            // Always get full notes for export, preserve formatting
            const notesFull = box.querySelector('.notes-full');
            if (notesFull) {
              value = notesFull.innerHTML;
            } else {
              value = box.querySelector('.notes-container')?.innerHTML || box.querySelector('p')?.innerHTML || '';
            }
            notesValue = value;
          } else {
            value = box.querySelector('p, div')?.innerText || '';
            if (label === 'Project ID') projectId = value;
            if (label === 'Title') projectTitle = value;
            details.push([label, value]);
          }
        });
        // Prepare rows for autoTable (two columns per row)
        let tableRows = [];
        for (let i = 0; i < details.length; i += 2) {
          let row = [];
          // First column
          if (details[i]) {
            row.push({content: `${details[i][0]}:`, styles: {fontStyle: 'bold', textColor: [0,0,0]}}, details[i][1]);
          } else {
            row.push('', '');
          }
          // Second column
          if (details[i+1]) {
            row.push({content: `${details[i+1][0]}:`, styles: {fontStyle: 'bold', textColor: [0,0,0]}}, details[i+1][1]);
          } else {
            row.push('', '');
          }
          tableRows.push(row);
        }
        // Ensure each row has 4 cells (2 columns)
        tableRows = tableRows.map(row => {
          while (row.length < 4) row.push('');
          return row;
        });
        // Use autoTable for the main details
        if (doc.autoTable) {
          doc.autoTable({
            startY: y,
            body: tableRows,
            theme: 'grid',
            styles: {cellPadding: 2, fontSize: 12, valign: 'top'},
            margin: { left: marginLeft, right: marginRight },
            tableWidth: usableWidth,
            columnStyles: {
              0: {cellWidth: 40, fontStyle: 'bold', textColor: [0,0,0]},
              2: {cellWidth: 40, fontStyle: 'bold', textColor: [0,0,0]}
            },
            didDrawCell: function (data) {
              // No-op, but can be used for custom rendering
            }
          });
          y = doc.lastAutoTable.finalY + 10;
        } else {
          // Fallback: just print as text if autoTable not loaded
          let col1x = marginLeft, col2x = marginLeft + usableWidth / 2;
          for (let i = 0; i < details.length; i += 2) {
            doc.setFont(undefined, 'bold');
            doc.text(`${details[i][0]}:`, col1x, y);
            doc.setFont(undefined, 'normal');
            doc.text(details[i][1], col1x + 25, y);
            if (details[i+1]) {
              doc.setFont(undefined, 'bold');
              doc.text(`${details[i+1][0]}:`, col2x, y);
              doc.setFont(undefined, 'normal');
              doc.text(details[i+1][1], col2x + 25, y);
            }
            y += 10;
          }
        }
        // Add Notes as a full-width, formatted block below the table
        if (notesValue) {
          y += 10;
          // Notes label
          doc.setFont(undefined, 'bold');
          doc.setFontSize(14);
          doc.text('Notes:', marginLeft, y);
          y += 8;
          doc.setFont(undefined, 'normal');
          doc.setFontSize(12);
          // Render HTML formatting for notes
          let tempDiv = document.createElement('div');
          tempDiv.innerHTML = notesValue;
          function renderHtmlToPdf(element, x, y) {
            let startY = y;
            const pageHeight = doc.internal.pageSize.getHeight();
            const marginTop = 15;
            const marginBottom = 15;
            const usableHeight = pageHeight - marginBottom;
            const lineHeight = 6;
            for (let node of element.childNodes) {
              if (node.nodeType === 3) { // text
                let lines = doc.splitTextToSize(node.textContent, usableWidth);
                if (lines.length > 0) {
                  if (startY > usableHeight) {
                    doc.addPage();
                    startY = marginTop;
                  }
                  doc.text(lines, x, startY, { maxWidth: usableWidth });
                  startY += lines.length * lineHeight;
                }
              } else if (node.nodeType === 1) { // element
                if (node.tagName === 'B' || node.tagName === 'STRONG') {
                  doc.setFont(undefined, 'bold');
                  startY = renderHtmlToPdf(node, x, startY);
                  doc.setFont(undefined, 'normal');
                } else if (node.tagName === 'I' || node.tagName === 'EM') {
                  doc.setFont(undefined, 'italic');
                  startY = renderHtmlToPdf(node, x, startY);
                  doc.setFont(undefined, 'normal');
                } else if (node.tagName === 'UL' || node.tagName === 'OL') {
                  let isOrdered = node.tagName === 'OL';
                  let idx = 1;
                  for (let li of node.children) {
                    let prefix = isOrdered ? (idx++) + '. ' : '\u2022 ';
                    let bulletLines = doc.splitTextToSize(prefix + li.innerText, usableWidth - 5);
                    if (startY > usableHeight) {
                      doc.addPage();
                      startY = marginTop;
                    }
                    doc.text(bulletLines, x + 5, startY, { maxWidth: usableWidth - 5 });
                    startY += bulletLines.length * lineHeight;
                  }
                } else if (node.tagName === 'BR') {
                  startY += lineHeight;
                } else {
                  startY = renderHtmlToPdf(node, x, startY);
                }
              }
            }
            return startY;
          }
          y = renderHtmlToPdf(tempDiv, marginLeft, y) + 4;
        }
        // Filename: ProjectID_title_Prod_ticket
        let safeTitle = projectTitle.replace(/[^a-zA-Z0-9_\-]/g, '_').substring(0, 40);
        let safeId = projectId.replace(/[^a-zA-Z0-9_\-]/g, '_');
        let filename = `${safeId}_${safeTitle}_ProdTicket.pdf`;
        doc.save(filename);
      }
      if (window.jspdf && window.jspdf.jsPDF) {
        doExport();
      } else {
        // Wait for script to load
        const script = document.getElementById('jspdf-cdn');
        script.onload = doExport;
      }
    };

    // Project Links Functionality
    let projectLinks = [];
    let currentProjectId = '';
    let isEditMode = false;

    // Initialize Project Links when page loads
    document.addEventListener('DOMContentLoaded', function() {
      const urlParams = new URLSearchParams(window.location.search);
      currentProjectId = urlParams.get('projectId');
      if (currentProjectId) {
        loadProjectLinks();
        initializeProjectLinksEventListeners();
      }
    });

    function initializeProjectLinksEventListeners() {
      // Add Main Bucket button
      document.getElementById('addMainBucketBtn').addEventListener('click', showAddMainBucketModal);
      
      // Initially hide the Add Category button
      document.getElementById('addMainBucketBtn').style.display = 'none';
      
      // Check user permissions and show edit mode button if applicable
      const userRole = localStorage.getItem('role');
      const userName = localStorage.getItem('username');
      
      if (userRole === 'Admin' || userRole === 'Project Manager') {
        const toggleEditBtn = document.getElementById('toggleEditModeBtn');
        toggleEditBtn.style.display = 'inline-block';
        toggleEditBtn.addEventListener('click', toggleEditMode);
      }
    }

    async function loadProjectLinks() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/project-links/${currentProjectId}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        if (response.ok) {
          projectLinks = await response.json();
        } else if (response.status === 404) {
          // No links exist yet, start with empty array
          projectLinks = [];
        } else {
          throw new Error('Failed to load project links');
        }
        
        renderProjectLinks();
      } catch (error) {
        console.error('Error loading project links:', error);
        projectLinks = [];
        renderProjectLinks();
      }
    }

    function renderProjectLinks() {
      const container = document.getElementById('project-links-content');
      
      if (projectLinks.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #666; font-style: italic;">No project links added yet. Click "Add Category" to get started.</p>';
        return;
      }

      container.innerHTML = '';
      
      projectLinks.forEach((bucket, bucketIndex) => {
        const bucketElement = createMainBucketElement(bucket, bucketIndex);
        container.appendChild(bucketElement);
      });
    }

    function toggleEditMode() {
      isEditMode = !isEditMode;
      const toggleBtn = document.getElementById('toggleEditModeBtn');
      const addCategoryBtn = document.getElementById('addMainBucketBtn');
      
      if (isEditMode) {
        toggleBtn.innerHTML = '<i class="fas fa-eye"></i> View Mode';
        toggleBtn.className = 'btn btn-sm btn-success';
        addCategoryBtn.style.display = 'inline-block';
      } else {
        toggleBtn.innerHTML = '<i class="fas fa-edit"></i> Edit Mode';
        toggleBtn.className = 'btn btn-sm btn-secondary';
        addCategoryBtn.style.display = 'none';
      }
      
      // Re-render to show/hide edit controls
      renderProjectLinks();
    }

    function createMainBucketElement(bucket, bucketIndex) {
      const bucketDiv = document.createElement('div');
      bucketDiv.className = 'main-bucket';
      bucketDiv.dataset.bucketIndex = bucketIndex;

      const headerDiv = document.createElement('div');
      headerDiv.className = 'main-bucket-header';

      const titleElement = document.createElement('h3');
      titleElement.className = 'main-bucket-title';
      titleElement.textContent = bucket.title;

      const controlsDiv = document.createElement('div');
      controlsDiv.className = 'main-bucket-controls';

      if (isEditMode) {
        const editBtn = document.createElement('button');
        editBtn.className = 'edit-bucket-btn';
        editBtn.innerHTML = '<i class="fas fa-edit"></i>';
        editBtn.title = 'Edit Category';
        editBtn.onclick = () => showEditMainBucketModal(bucketIndex);

        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'delete-bucket-btn';
        deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
        deleteBtn.title = 'Delete Category';
        deleteBtn.onclick = () => deleteMainBucket(bucketIndex);

        controlsDiv.appendChild(editBtn);
        controlsDiv.appendChild(deleteBtn);
      }
      headerDiv.appendChild(titleElement);
      headerDiv.appendChild(controlsDiv);

      const linksList = document.createElement('ul');
      linksList.className = 'sub-bucket-list';

      bucket.links.forEach((link, linkIndex) => {
        const linkItem = createSubBucketElement(link, bucketIndex, linkIndex);
        linksList.appendChild(linkItem);
      });

      bucketDiv.appendChild(headerDiv);
      bucketDiv.appendChild(linksList);
      
      if (isEditMode) {
        const addLinkBtn = document.createElement('button');
        addLinkBtn.className = 'add-link-btn';
        addLinkBtn.innerHTML = '<i class="fas fa-plus"></i> Add Link';
        addLinkBtn.onclick = () => showAddLinkModal(bucketIndex);
        bucketDiv.appendChild(addLinkBtn);
      }

      return bucketDiv;
    }

    function createSubBucketElement(link, bucketIndex, linkIndex) {
      const li = document.createElement('li');
      li.className = 'sub-bucket-item';

      const linkElement = document.createElement('a');
      linkElement.className = 'sub-bucket-link';
      linkElement.href = link.url;
      linkElement.target = '_blank';
      linkElement.textContent = link.name;
      linkElement.title = link.url;

      const controlsDiv = document.createElement('div');
      controlsDiv.className = 'sub-bucket-controls';

      if (isEditMode) {
        const editBtn = document.createElement('button');
        editBtn.className = 'edit-link-btn';
        editBtn.innerHTML = '<i class="fas fa-edit"></i>';
        editBtn.title = 'Edit Link';
        editBtn.onclick = () => showEditLinkModal(bucketIndex, linkIndex);

        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'delete-link-btn';
        deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
        deleteBtn.title = 'Delete Link';
        deleteBtn.onclick = () => deleteLink(bucketIndex, linkIndex);

        controlsDiv.appendChild(editBtn);
        controlsDiv.appendChild(deleteBtn);
      }
      li.appendChild(linkElement);
      li.appendChild(controlsDiv);

      return li;
    }

    // Modal Functions
    function showModal(title, content) {
      const modal = document.createElement('div');
      modal.className = 'modal-overlay';
      modal.innerHTML = `
        <div class="modal-content">
          <div class="modal-header">
            <h3 class="modal-title">${title}</h3>
            <button class="close-modal" onclick="closeModal(this)">&times;</button>
          </div>
          ${content}
        </div>
      `;
      document.body.appendChild(modal);
    }

    function closeModal(button) {
      const modal = button.closest('.modal-overlay');
      modal.remove();
    }

    function showAddMainBucketModal() {
      const content = `
        <div class="form-group">
          <label for="bucketTitle">Category Title:</label>
          <input type="text" id="bucketTitle" placeholder="e.g., Schedule Link, Reference Docs">
        </div>
        <div class="modal-actions">
          <button class="btn-cancel" onclick="closeModal(this)">Cancel</button>
          <button class="btn-save" onclick="addMainBucket()">Save</button>
        </div>
      `;
      showModal('Add New Category', content);
    }

    function showEditMainBucketModal(bucketIndex) {
      const bucket = projectLinks[bucketIndex];
      const content = `
        <div class="form-group">
          <label for="bucketTitle">Category Title:</label>
          <input type="text" id="bucketTitle" value="${bucket.title}" placeholder="e.g., Schedule Link, Reference Docs">
        </div>
        <div class="modal-actions">
          <button class="btn-cancel" onclick="closeModal(this)">Cancel</button>
          <button class="btn-save" onclick="editMainBucket(${bucketIndex})">Save</button>
        </div>
      `;
      showModal('Edit Category', content);
    }

    function showAddLinkModal(bucketIndex) {
      const content = `
        <div class="form-group">
          <label for="linkName">Link Name:</label>
          <input type="text" id="linkName" placeholder="e.g., Team Calendar, Sprint Plan">
        </div>
        <div class="form-group">
          <label for="linkUrl">URL:</label>
          <input type="url" id="linkUrl" placeholder="https://example.com">
        </div>
        <div class="modal-actions">
          <button class="btn-cancel" onclick="closeModal(this)">Cancel</button>
          <button class="btn-save" onclick="addLink(${bucketIndex})">Save</button>
        </div>
      `;
      showModal('Add New Link', content);
    }

    function showEditLinkModal(bucketIndex, linkIndex) {
      const link = projectLinks[bucketIndex].links[linkIndex];
      const content = `
        <div class="form-group">
          <label for="linkName">Link Name:</label>
          <input type="text" id="linkName" value="${link.name}" placeholder="e.g., Team Calendar, Sprint Plan">
        </div>
        <div class="form-group">
          <label for="linkUrl">URL:</label>
          <input type="url" id="linkUrl" value="${link.url}" placeholder="https://example.com">
        </div>
        <div class="modal-actions">
          <button class="btn-cancel" onclick="closeModal(this)">Cancel</button>
          <button class="btn-save" onclick="editLink(${bucketIndex}, ${linkIndex})">Save</button>
        </div>
      `;
      showModal('Edit Link', content);
    }

    // CRUD Operations
    async function addMainBucket() {
      const title = document.getElementById('bucketTitle').value.trim();
      if (!title) {
        alert('Please enter a category title');
        return;
      }

      const newBucket = {
        title: title,
        links: []
      };

      projectLinks.push(newBucket);
      await saveProjectLinks();
      closeModal(document.querySelector('.close-modal'));
      renderProjectLinks();
    }

    async function editMainBucket(bucketIndex) {
      const title = document.getElementById('bucketTitle').value.trim();
      if (!title) {
        alert('Please enter a category title');
        return;
      }

      projectLinks[bucketIndex].title = title;
      await saveProjectLinks();
      closeModal(document.querySelector('.close-modal'));
      renderProjectLinks();
    }

    async function deleteMainBucket(bucketIndex) {
      if (!confirm('Are you sure you want to delete this category and all its links?')) {
        return;
      }

      projectLinks.splice(bucketIndex, 1);
      await saveProjectLinks();
      renderProjectLinks();
    }

    async function addLink(bucketIndex) {
      const name = document.getElementById('linkName').value.trim();
      const url = document.getElementById('linkUrl').value.trim();

      if (!name || !url) {
        alert('Please enter both link name and URL');
        return;
      }

      if (!isValidUrl(url)) {
        alert('Please enter a valid URL');
        return;
      }

      const newLink = {
        name: name,
        url: url
      };

      projectLinks[bucketIndex].links.push(newLink);
      await saveProjectLinks();
      closeModal(document.querySelector('.close-modal'));
      renderProjectLinks();
    }

    async function editLink(bucketIndex, linkIndex) {
      const name = document.getElementById('linkName').value.trim();
      const url = document.getElementById('linkUrl').value.trim();

      if (!name || !url) {
        alert('Please enter both link name and URL');
        return;
      }

      if (!isValidUrl(url)) {
        alert('Please enter a valid URL');
        return;
      }

      projectLinks[bucketIndex].links[linkIndex] = {
        name: name,
        url: url
      };

      await saveProjectLinks();
      closeModal(document.querySelector('.close-modal'));
      renderProjectLinks();
    }

    async function deleteLink(bucketIndex, linkIndex) {
      if (!confirm('Are you sure you want to delete this link?')) {
        return;
      }

      projectLinks[bucketIndex].links.splice(linkIndex, 1);
      await saveProjectLinks();
      renderProjectLinks();
    }

    async function saveProjectLinks() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/project-links/${currentProjectId}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ links: projectLinks })
        });

        if (!response.ok) {
          throw new Error('Failed to save project links');
        }
      } catch (error) {
        console.error('Error saving project links:', error);
        alert('Failed to save project links. Please try again.');
      }
    }

    function isValidUrl(string) {
      try {
        new URL(string);
        return true;
      } catch (_) {
        return false;
      }
    }

    // Make functions globally available
    window.closeModal = closeModal;
    window.addMainBucket = addMainBucket;
    window.editMainBucket = editMainBucket;
    window.addLink = addLink;
    window.editLink = editLink;
    window.deleteMainBucket = deleteMainBucket;
    window.deleteLink = deleteLink;
  </script>
</body>
</html> 