<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Composure Dashboard</title>
    <link rel="stylesheet" href="styles/style.css" />
    <link rel="stylesheet" href="styles/invoices.css" />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Bootstrap CSS -->
    <link
      href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="container-fluid-invoices"> 
        <div class="row">          
          <div class="main-wrapper-invoices">
            <header class="top-bar-wip-report">
              <div class="left-section">
              <button onclick="goHome()" class="home-button">
                <i class="fas fa-home"></i> Home
              </button>
              <!-- Add the dropdowns here -->
              <div class="dropdown-container">
                <div class="dropdown">
                  <button class="dropdown-button">Production</button>
                  <div class="dropdown-content">
                    <a href="Move-to-Production.html">Move to Production</a>
                  </div>
                </div>
                <div class="dropdown">
                  <button class="dropdown-button">Reports</button>
                  <div class="dropdown-content">
                    <a href="WIP-report.html">WIP Report</a>
                  </div>
                </div>
                <div class="dropdown">
                  <button class="dropdown-button">Project Management</button>
                  <div class="dropdown-content">
                    <a href="/Invoices.html">Invoice</a>
                  </div>
                </div>
                <div class="dropdown">
                  <button class="dropdown-button">Tickets</button>
                  <div class="dropdown-content">
                    <a href="/Tickets.html">Tickets</a>
                  </div>
                </div>
              </div>
            </div>
            <!-- End the dropdowns here -->
            <div class="right-section">
              <div class="user-info">
                <div class="user-photo-container">
                  <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp" alt="User Photo" class="user-photo" onerror="this.onerror=null;this.src='https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';" />
                  <div class="user-dropdown-content">
                    <a href="/user-info.html" onclick="openSettings()"><i class="fas fa-cog"></i> Settings</a>
                  </div>
                </div>
                <div class="user-details">
                  <span class="user-name">Welcome, User</span>
                  <span class="user-role">Role</span>
                </div>
              </div>
                <button onclick="logout()" class="logout-button">Logout</button>
              </div>
            </header>
        </div>
    </div>
    <div class="Invoice-container-fluid">
      <div class="row">
        <div class="col-md-6">    
          <div class="Invoice-container">  
            <div class="container-completedprojects">
              <h2>Completed Projects</h2>
              <div id="project-list-container">
                <!-- Completed projects will be listed here -->
              </div>
            </div>
          </div>
        </div>

        <!-- Approval Status Container -->
        <div class="col-md-6">
          <div id="approval-status-container" class="approval-status-container" style="display: none;">
            <h3>Approval Status</h3>
            <div id="approval-status-content">
              <!-- Approval stages will be dynamically loaded here -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        // Check if user is authorized to access this page
        const userRole = localStorage.getItem("role");
        if (userRole !== 'Admin' && userRole !== 'Project Manager') {
          alert('Access denied. Only Administrators and Project Managers can access this page.');
          window.location.href = '/index.html';
          return;
        }
        
        const userName = localStorage.getItem("username");
        
        // Update the user profile section
        const userNameElement = document.querySelector(".user-name");
        const userRoleElement = document.querySelector(".user-role");
        
        if (userNameElement) {
          userNameElement.textContent = `Welcome, ${userName || "User"}`;
        }
        if (userRoleElement) {
          userRoleElement.textContent = userRole || "Role not found";
        }
        
        // Hide Invoice link for unauthorized roles
        const invoiceLinks = document.querySelectorAll('a[href="/Invoices.html"]');
        invoiceLinks.forEach(link => {
          const parentDropdown = link.closest('.dropdown');
          if (userRole !== 'Admin' && userRole !== 'Project Manager') {
            if (parentDropdown) {
              parentDropdown.style.display = 'none';
            } else {
              link.style.display = 'none';
            }
          }
        });
        
        console.log("User Role:", userRole); // Debugging
        console.log("User Name:", userName); // Debugging
      });
      </script>
    <script>
        
        function goHome() {
  window.location.href = "index.html";
}

      function logout() {
            // Clear all user-related data
            const itemsToClear = ['token', 'username', 'role', 'userAvatar', 'userId'];
            itemsToClear.forEach(item => localStorage.removeItem(item));
            
            // Reset ALL user photos to default before redirecting
            const userPhotos = document.querySelectorAll('.user-photo, #userAvatar');
            userPhotos.forEach(photo => {
                photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
            });
            
            // Redirect to login page
            window.location.href = '/login.html';
        }

document.addEventListener("DOMContentLoaded", async function () {
  const projectListContainer = document.getElementById("project-list-container");
  const approvalStatusContainer = document.getElementById("approval-status-container");
  const approvalStatusContent = document.getElementById("approval-status-content");

  try {
    const token = localStorage.getItem("token"); // Retrieve the token
    if (!token) {
      alert("No authentication token found. Please log in again.");
      window.location.href = "/login.html";
      return;
    }

    const response = await fetch("/api/completedprojects", {
      headers: {
        Authorization: `Bearer ${token}`, // Include the token in the Authorization header
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch completed projects");
    }

    const projects = await response.json();

    if (projects.length === 0) {
      projectListContainer.innerHTML = "<p>No completed projects found.</p>";
      return;
    }

    // Create a table to display the projects
    const table = document.createElement("table");
    table.classList.add("table", "table-striped");

    // Add table headers
    const thead = document.createElement("thead");
    thead.innerHTML = `
      <tr>
        <th>Project ID</th>
        <th>Title</th>
        <th>Client Name</th>
        <th>Start Date</th>
        <th>End Date</th>
        <th>Project Manager</th>
        <th>Action</th>
      </tr>
    `;
    table.appendChild(thead);

    // Add table rows
    const tbody = document.createElement("tbody");
    projects.forEach((project) => {
      const row = document.createElement("tr");
      row.innerHTML = `
        <td>${project._id}</td>
        <td>${project.title}</td>
        <td>${project.clientName}</td>
        <td>${new Date(project.startDate).toLocaleDateString()}</td>
        <td>${new Date(project.endDate).toLocaleDateString()}</td>
        <td>${project.projectManager}</td>
        <td><button class="btn btn-info" onclick="showApprovalStatus('${project._id}')">Status</button></td>
      `;
      tbody.appendChild(row);
    });
    table.appendChild(tbody);

    // Clear previous content and add the table
    projectListContainer.innerHTML = '';
    projectListContainer.appendChild(table);
  } catch (error) {
    console.error("Error fetching completed projects:", error);
    projectListContainer.innerHTML = "<p>Error loading completed projects. Please try again later.</p>";
  }

  // Add this at the top of your script
  let isLoadingStatus = false;
  let lastClickTime = 0;
  const CLICK_DELAY = 1000; // 1 second delay between clicks

  window.showApprovalStatus = async function (projectId) {
    // Prevent concurrent calls and rapid clicking
    const now = Date.now();
    if (isLoadingStatus || (now - lastClickTime) < CLICK_DELAY) {
      console.log("⚠️ Status loading in progress or clicked too soon");
      return;
    }

    try {
      isLoadingStatus = true;
      lastClickTime = now;
      console.log(`🔍 Fetching approval status for projectId: ${projectId}`);

      const token = localStorage.getItem("token");
      if (!token) {
        alert("No authentication token found. Please log in again.");
        window.location.href = "/login.html";
        return;
      }

      // Show loading state
      const approvalStatusContainer = document.getElementById("approval-status-container");
      approvalStatusContainer.style.display = "block";
      approvalStatusContent.innerHTML = '<div class="loading">Loading status...</div>';

      const response = await fetch(`/api/approval-status/${projectId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch approval status");
      }

      const approvalStatus = await response.json();
      console.log("✅ Approval Status Data:", approvalStatus);

      const currentUser = localStorage.getItem("username");

      // Define the fixed order of stages
      const stageOrder = [
        "Reconcilation completed by PM",
        "Reconcilation approved by Peter",
        "Invoice Sent to Client",
        "Invoice Paid",
        "Project Archived",
      ];

      // Sort stages based on the fixed order
      approvalStatus.stages.sort((a, b) => {
        return stageOrder.indexOf(a.stage) - stageOrder.indexOf(b.stage);
      });

      // Show the container
      document.getElementById("approval-status-container").style.display = "block";
      approvalStatusContent.innerHTML = ""; // Clear previous content

      // Use for...of instead of forEach to handle async operations properly
      for (const [index, stage] of approvalStatus.stages.entries()) {
        const stageElement = document.createElement("div");
        stageElement.classList.add("approval-stage");
        if (stage.completed) {
          stageElement.classList.add("completed");
        }

        stageElement.innerHTML = `
          <div class="stage-icon">${index + 1}</div>
          <div class="stage-content">
            <p>${stage.stage}</p>
            <div class="attachment-container" id="attachment-container-${index}">
              <!-- Attachment icon will be dynamically added here -->
            </div>
            <div class="uploaded-files" id="uploaded-files-${index}">
              <!-- Uploaded files will be dynamically added here -->
            </div>
            <div class="stage-actions" id="stage-actions-${index}">
              <!-- Complete Stage button will be added here -->
            </div>
            ${
              stage.completed
                ? `<p class="activity-log">Approved by ${stage.approvedBy || "N/A"} on ${
                    stage.approvedAt ? new Date(stage.approvedAt).toLocaleString() : "N/A"
                  }</p>`
                : ""
            }
          </div>
        `;

        // Add button based on conditions
        if (
          !stage.completed && 
          ((index === 0 && (currentUser === stage.buttonVisibleTo || currentUser === "Admin")) || 
            (index > 0 &&
              (currentUser === stage.buttonVisibleTo || currentUser === "Admin") &&
              approvalStatus.stages[index - 1].completed))
        ) {
          const button = document.createElement("button");
          button.classList.add("btn", "btn-primary", "complete-stage-btn");
          button.textContent = "Complete Stage";
          button.onclick = () => completeStage(projectId, index, currentUser);
          stageElement.querySelector(`#stage-actions-${index}`).appendChild(button);
        }

        // Add attachment form for Stage 1 and Stage 2
        if (index === 0 || index === 1) {
          const attachmentForm = document.createElement("form");
          attachmentForm.classList.add("attachment-form");
          attachmentForm.innerHTML = `
            <label class="attachment-icon">
              <i class="fas fa-paperclip"></i>
              <input type="file" class="attachment-input" />
            </label>
            <span class="selected-file-name"></span>
            <button type="button" class="upload-btn">
              <i class="fas fa-cloud-upload-alt"></i>
              Upload
            </button>
          `;

          const fileInput = attachmentForm.querySelector(".attachment-input");
          const fileNameDisplay = attachmentForm.querySelector(".selected-file-name");

          fileInput.addEventListener("change", (e) => {
            const fileName = e.target.files[0]?.name || "No file selected";
            fileNameDisplay.textContent = fileName;
          });

          const uploadButton = attachmentForm.querySelector(".upload-btn");
          uploadButton.onclick = async () => {
            const fileInput = attachmentForm.querySelector(".attachment-input");
            const file = fileInput.files[0];
            if (!file) {
              alert("Please select a file to upload.");
              return;
            }

            const formData = new FormData();
            formData.append("file", file);

            try {
              const uploadResponse = await fetch(`/api/approval-status/${projectId}/stage/${index}/upload`, {
                method: "POST",
                body: formData,
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              });

              if (!uploadResponse.ok) {
                throw new Error("Failed to upload file");
              }

              const result = await uploadResponse.json();
              console.log("File uploaded successfully:", result);
              alert("File uploaded successfully!");

              const uploadedFilesContainer = stageElement.querySelector(`#uploaded-files-${index}`);
              const fileLink = document.createElement("a");
              fileLink.href = `/uploads/${result.file.filename}`;
              fileLink.textContent = result.file.originalName;
              fileLink.target = "_blank";
              fileLink.classList.add("uploaded-file-link");
              uploadedFilesContainer.appendChild(fileLink);
            } catch (error) {
              console.error("Error uploading file:", error);
              alert("Error uploading file.");
            }
          };

          const attachmentContainer = stageElement.querySelector(`#attachment-container-${index}`);
          attachmentContainer.appendChild(attachmentForm);
        }

        // Fetch and display existing files
        const uploadedFilesContainer = stageElement.querySelector(`#uploaded-files-${index}`);
        try {
          const filesResponse = await fetch(`/api/approval-status/${projectId}/stage/${index}/files`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (filesResponse.ok) {
            const files = await filesResponse.json();
            if (files.length > 0) {
              files.forEach((file) => {
                const fileLink = document.createElement("a");
                fileLink.href = `/uploads/${file.filename}`;
                fileLink.textContent = file.originalName;
                fileLink.target = "_blank";
                fileLink.classList.add("uploaded-file-link");
                uploadedFilesContainer.appendChild(fileLink);
              });
              uploadedFilesContainer.style.display = "block";
            } else {
              uploadedFilesContainer.style.display = "none";
            }
          }
        } catch (error) {
          console.error(`Error fetching files for stage ${index}:`, error);
          uploadedFilesContainer.style.display = "none";
        }

        approvalStatusContent.appendChild(stageElement);
      }
    } catch (error) {
      console.error("Error:", error);
      alert("Failed to fetch approval status");
    } finally {
      isLoadingStatus = false;
    }
  };

  // Function to mark a stage as completed
  async function completeStage(projectId, stageIndex, approvedBy) {
    try {
      const token = localStorage.getItem("token"); // Retrieve the token again
      if (!token) {
        alert("No authentication token found. Please log in again.");
        window.location.href = "/login.html";
        return;
      }

      const response = await fetch(`/api/approval-status/${projectId}/stage/${stageIndex}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({ approvedBy }),
      });

      if (!response.ok) {
        throw new Error("Failed to update stage");
      }

      const updatedStatus = await response.json();
      console.log("Stage updated successfully:", updatedStatus);
      showApprovalStatus(projectId); // Re-render the approval stages
    } catch (error) {
      console.error("Error updating stage:", error);
    }
  }
});
      
    </script>
    <script>
      // Add this function to update user info including avatar
      function updateUserInterface() {
          const userRole = localStorage.getItem('role');
          const userName = localStorage.getItem('username');
          const userAvatar = localStorage.getItem('userAvatar');
          
          // Update username and role
          const userNameElement = document.querySelector('.user-name');
          const userRoleElement = document.querySelector('.user-role');
          if (userNameElement) userNameElement.textContent = `Welcome, ${userName || 'User'}`;
          if (userRoleElement) userRoleElement.textContent = userRole || 'Role';
          
          // Update all user photos on the page
          const userPhotos = document.querySelectorAll('.user-photo');
          userPhotos.forEach(photo => {
              if (userAvatar) {
                  photo.src = userAvatar;
              } else {
                  photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'; // Default photo
              }
          });
      }
      
      // Call this function when the page loads
      document.addEventListener('DOMContentLoaded', updateUserInterface);
      
      // Optional: Refresh user interface periodically to catch any changes
      setInterval(updateUserInterface, 30000); // Update every 30 seconds
      </script>
</body>
</html>
