<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Composure Dashboard</title>
    <link rel="stylesheet" href="styles/style.css" />
    <link rel="stylesheet" href="styles/wip-report.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />
    <!-- Bootstrap CSS -->
    <link
      href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <style>
      /* ... existing styles ... */
      
      /* Loading Spinner Styles */
      .loading-container {
        display: none;
        padding: 40px;
        text-align: center;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 20px 0;
      }

      .loading-spinner {
        display: inline-block;
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 15px;
      }

      .loading-text {
        color: #3498db;
        font-size: 1.1em;
        font-weight: 500;
        margin-top: 10px;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div class="container-fluid-wip-report">
      <div class="row">
        <div class="main-wrapper-wip-report">
          <header class="top-bar-wip-report">
            <div class="left-section">
              <button onclick="goHome()" class="home-button">
                <i class="fas fa-home"></i> Home
              </button>
              <div class="dropdown-container">
                <div class="dropdown">
                  <button class="dropdown-button">Production</button>
                  <div class="dropdown-content">
                    <a href="Move-to-Production.html">Move to Production</a>
                  </div>
                </div>
                <div class="dropdown">
                  <button class="dropdown-button">Reports</button>
                  <div class="dropdown-content">
                    <a href="WIP-report.html">WIP Report</a>
                  </div>
                </div>
                <div class="dropdown">
                  <button class="dropdown-button">Project Management</button>
                  <div class="dropdown-content">
                    <a href="Invoices.html">Invoice</a>
                  </div>
                </div>
                <div class="dropdown">
                  <button class="dropdown-button">Tickets</button>
                  <div class="dropdown-content">
                    <a href="Tickets.html">Tickets</a>
                  </div>
                </div>
              </div>
            </div>
            <div class="right-section">
              <div class="user-info">
                <div class="user-photo-container">
                  <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp" alt="User Photo" class="user-photo" onerror="this.onerror=null;this.src='https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';" />
                  <div class="user-dropdown-content">
                    <a href="/user-info.html" onclick="openSettings()"><i class="fas fa-cog"></i> Settings</a>
                  </div>
                </div>
                <div class="user-details">
                  <span class="user-name">Welcome, User</span>
                  <span class="user-role">Role</span>
                </div>
              </div>
              <button onclick="logout()" class="logout-button">Logout</button>
            </div>
          </header>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="container-wip">
        <h2>WIP Reports</h2>
        
        <!-- Search bar -->
        <div class="wip-filters">
          <div class="search-box">
            <input type="text" id="wip-search" placeholder="Search by Project ID, Name, or Manager...">
            <i class="fas fa-search"></i>
          </div>
        </div>
        
        <!-- Tabs navigation -->
        <ul class="nav nav-tabs" id="wipTabs" role="tablist">
          <li class="nav-item" role="presentation">
            <a class="nav-link active" id="cumulative-tab" data-toggle="tab" href="#cumulative" role="tab" aria-controls="cumulative" aria-selected="true">
              Project Cumulative WIP
            </a>
          </li>
          <li class="nav-item" role="presentation">
            <a class="nav-link" id="detailed-tab" data-toggle="tab" href="#detailed" role="tab" aria-controls="detailed" aria-selected="false">
              Detailed WIP Report
            </a>
          </li>
          <li class="nav-item" role="presentation">
            <a class="nav-link" id="parent-tab" data-toggle="tab" href="#parent" role="tab" aria-controls="parent" aria-selected="false">
              Parent Project WIP
            </a>
          </li>
        </ul>
        
        <!-- Tab content -->
        <div class="tab-content" id="wipTabContent">
          <!-- Cumulative WIP Tab -->
          <div class="tab-pane fade show active" id="cumulative" role="tabpanel" aria-labelledby="cumulative-tab">
            <div class="table-responsive">
              <table class="table table-bordered" id="cumulative-wip-table">
                <thead>
                  <tr>
                    <th>Project ID</th>
                    <th>Project Name</th>
                    <th>Project Manager</th>
                    <th>Total Levels</th>
                    <th>Yet to Start (%)</th>
                    <th>In Progress (%)</th>
                    <th>Completed (%)</th>
                  </tr>
                </thead>
                <tbody id="cumulative-wip-table-body">
                  <!-- Loading spinner will be inserted here -->
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Detailed WIP Tab -->
          <div class="tab-pane fade" id="detailed" role="tabpanel" aria-labelledby="detailed-tab">
            <div class="table-responsive">
              <table class="table table-bordered" id="wip-report-table">
                <thead>
                  <tr>
                    <th>Project ID</th>
                    <th>Project Name</th>
                    <th>Project Manager</th>
                    <th>Yet to Start (%)</th>
                    <th>In Progress (%)</th>
                    <th>Completed (%)</th>
                  </tr>
                </thead>
                <tbody id="wip-report-table-body">
                  <!-- Loading spinner will be inserted here -->
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Parent Project WIP Tab -->
          <div class="tab-pane fade" id="parent" role="tabpanel" aria-labelledby="parent-tab">
            <div class="table-responsive">
              <table class="table table-bordered" id="parent-wip-table">
                <thead>
                  <tr>
                    <th>Parent Project ID</th>
                    <th>Parent Project Name</th>
                    <th>Project Manager</th>
                    <th>Child Projects</th>
                    <th>Yet to Start (%)</th>
                    <th>In Progress (%)</th>
                    <th>Completed (%)</th>
                  </tr>
                </thead>
                <tbody id="parent-wip-table-body">
                  <!-- Loading spinner will be inserted here -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
        
        <button id="export-button" class="btn btn-primary">
          <i class="fas fa-file-excel"></i> Export All Reports
        </button>
      </div>
    </div>
    <!-- Bootstrap JS and dependencies -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const userNameElement = document.querySelector(".user-name");
        const userRoleElement = document.querySelector(".user-role");
      
        // Retrieve the user's role and name from localStorage
        const userRole = localStorage.getItem("role");
        const userName = localStorage.getItem("username"); // Retrieve the username from localStorage
      
        // Update the user profile section
        if (userNameElement) {
          userNameElement.textContent = `Welcome, ${userName || "User"}`;
        }
        if (userRoleElement) {
          userRoleElement.textContent = userRole || "Role";
        }
      
        console.log("User Role:", userRole); // Debugging
        console.log("User Name:", userName); // Debugging
        
        // Load all WIP reports
        Promise.all([loadWIPReport(),loadCumulativeWIPReport(),loadParentProjectWIPReport()]);
        
        // Initialize search functionality
        const searchInput = document.getElementById('wip-search');
        searchInput.addEventListener('input', filterWIPTables);
      });
      
      // Function to filter all tables based on search input
      function filterWIPTables() {
        const searchTerm = document.getElementById('wip-search').value.toLowerCase();
        
        // Filter cumulative WIP table
        const cumulativeRows = document.querySelectorAll('#cumulative-wip-table-body tr');
        filterTableRows(cumulativeRows, searchTerm);
        
        // Filter detailed WIP table
        const detailedRows = document.querySelectorAll('#wip-report-table-body tr');
        filterTableRows(detailedRows, searchTerm);
        
        // Filter parent project WIP table
        const parentRows = document.querySelectorAll('#parent-wip-table-body tr');
        filterTableRows(parentRows, searchTerm);
      }
      
      // Helper function to filter table rows
      function filterTableRows(rows, searchTerm) {
        rows.forEach(row => {
          // For parent project table, also search in child project IDs
          if (row.cells.length >= 4 && row.cells[3].innerHTML.includes('<br>')) {
            // This is likely the parent project table with child project details
            const projectId = row.cells[0].textContent.toLowerCase();
            const projectName = row.cells[1].textContent.toLowerCase();
            const projectManager = row.cells[2].textContent.toLowerCase();
            const childProjects = row.cells[3].innerHTML.toLowerCase();
            
            if (projectId.includes(searchTerm) || 
                projectName.includes(searchTerm) || 
                projectManager.includes(searchTerm) ||
                childProjects.includes(searchTerm)) {
              row.style.display = '';
            } else {
              row.style.display = 'none';
            }
          } else {
            // Standard table row
            const projectId = row.cells[0].textContent.toLowerCase();
            const projectName = row.cells[1].textContent.toLowerCase();
            const projectManager = row.cells[2].textContent.toLowerCase();
            
            if (projectId.includes(searchTerm) || 
                projectName.includes(searchTerm) || 
                projectManager.includes(searchTerm)) {
              row.style.display = '';
            } else {
              row.style.display = 'none';
            }
          }
        });
      }

      async function loadWIPReport() {
        const tableBody = document.getElementById("wip-report-table-body");
        
        // Show loading spinner in table body
        tableBody.innerHTML = `
          <tr>
            <td colspan="6" class="text-center" style="padding: 40px;">
              <div class="loading-spinner"></div>
              <div class="loading-text">Loading WIP data...</div>
            </td>
          </tr>
        `;
        
        try {
          const wipResponse = await fetchWithAuth("/api/weighted-project-wip");
          if (!wipResponse.ok) {
            throw new Error("Failed to fetch WIP data");
          }
          const wipData = await wipResponse.json();
          
          // Create a map of project details
          const projectsMap = await fetchProjectDetails();

          // Sort the data
          wipData.sort((a, b) => a.projectId.localeCompare(b.projectId));

          // Clear and populate table with new rows
          let tableContent = '';
          wipData.forEach((item) => {
            const baseProjectId = item.projectId.split('-level-')[0];
            const projectDetails = projectsMap[baseProjectId] || { 
              name: "Unknown Project", 
              manager: "Not Assigned" 
            };
            
            tableContent += `
              <tr>
                <td>${item.projectId}</td>
                <td>${projectDetails.name}</td>
                <td>${projectDetails.manager}</td>
                <td>${item.totalYetToStart.toFixed(2)}%</td>
                <td>${item.totalInProgress.toFixed(2)}%</td>
                <td>${item.totalCompleted.toFixed(2)}%</td>
              </tr>
            `;
          });
          
          // Update table content
          tableBody.innerHTML = tableContent;
          
        } catch (error) {
          console.error("Error loading WIP report:", error);
          tableBody.innerHTML = `
            <tr>
              <td colspan="6" class="text-center text-danger">
                <i class="fas fa-exclamation-triangle"></i> 
                Error loading WIP data. Please try again.
              </td>
            </tr>
          `;
        }
      }

      async function loadCumulativeWIPReport() {
        const tableBody = document.getElementById("cumulative-wip-table-body");
        
        // Show loading spinner in table body
        tableBody.innerHTML = `
          <tr>
            <td colspan="7" class="text-center" style="padding: 40px;">
              <div class="loading-spinner"></div>
              <div class="loading-text">Loading cumulative WIP data...</div>
            </td>
          </tr>
        `;
        
        try {
          const wipResponse = await fetchWithAuth("/api/project-cumulative-wip");
          if (!wipResponse.ok) {
            throw new Error("Failed to fetch cumulative WIP data");
          }
          const wipData = await wipResponse.json();
          
          // Create a map of project details
          const projectsMap = await fetchProjectDetails();

          // Sort the data
          wipData.sort((a, b) => a.projectId.localeCompare(b.projectId));

          // Build table content
          let tableContent = '';
          wipData.forEach((item) => {
            const projectDetails = projectsMap[item.projectId] || { 
              name: "Unknown Project", 
              manager: "Not Assigned" 
            };
            
            tableContent += `
              <tr>
                <td>${item.projectId}</td>
                <td>${projectDetails.name}</td>
                <td>${projectDetails.manager}</td>
                <td>${item.totalLevels}</td>
                <td>${item.yetToStart.toFixed(2)}%</td>
                <td>${item.inProgress.toFixed(2)}%</td>
                <td>${item.completed.toFixed(2)}%</td>
              </tr>
            `;
          });
          
          // Update table content
          tableBody.innerHTML = tableContent;
          
        } catch (error) {
          console.error("Error loading cumulative WIP report:", error);
          tableBody.innerHTML = `
            <tr>
              <td colspan="7" class="text-center text-danger">
                <i class="fas fa-exclamation-triangle"></i> 
                Error loading cumulative WIP data. Please try again.
              </td>
            </tr>
          `;
        }
      }

      async function loadParentProjectWIPReport() {
        const tableBody = document.getElementById("parent-wip-table-body");
        
        // Show loading spinner in table body
        tableBody.innerHTML = `
          <tr>
            <td colspan="7" class="text-center" style="padding: 40px;">
              <div class="loading-spinner"></div>
              <div class="loading-text">Loading parent project WIP data...</div>
            </td>
          </tr>
        `;
        
        try {
          const allProjectsResponse = await fetchWithAuth("/api/all-projects");
          if (!allProjectsResponse.ok) {
            throw new Error("Failed to fetch all projects");
          }
          const allProjects = await allProjectsResponse.json();
          
          const wipResponse = await fetchWithAuth("/api/project-cumulative-wip");
          if (!wipResponse.ok) {
            throw new Error("Failed to fetch cumulative WIP data");
          }
          const wipData = await wipResponse.json();
          
          // Create a map of project details
          const projectsMap = await fetchProjectDetails();
          
          // Group projects by parent project ID
          const parentProjectMap = {};
          
          allProjects.forEach(project => {
            if (!project.parentProjectId) return;
            
            const parentId = project.parentProjectId;
            
            if (!parentProjectMap[parentId]) {
              parentProjectMap[parentId] = {
                projectIds: [],
                childProjectDetails: [],
                yetToStart: 0,
                inProgress: 0,
                completed: 0
              };
            }
            
            if (project._id !== parentId) {
              parentProjectMap[parentId].projectIds.push(project._id);
              parentProjectMap[parentId].childProjectDetails.push({
                id: project._id,
                name: project.title || "Unknown Project"
              });
            }
          });
          
          // Calculate WIP for parent projects
          Object.keys(parentProjectMap).forEach(parentId => {
            const childProjects = parentProjectMap[parentId].projectIds;
            let totalYetToStart = 0;
            let totalInProgress = 0;
            let totalCompleted = 0;
            let projectCount = 0;
            
            const parentWIP = wipData.find(item => item.projectId === parentId);
            if (parentWIP) {
              totalYetToStart += parentWIP.yetToStart || 0;
              totalInProgress += parentWIP.inProgress || 0;
              totalCompleted += parentWIP.completed || 0;
              projectCount++;
            }
            
            childProjects.forEach(projectId => {
              const projectWIP = wipData.find(item => item.projectId === projectId);
              if (projectWIP) {
                totalYetToStart += projectWIP.yetToStart || 0;
                totalInProgress += projectWIP.inProgress || 0;
                totalCompleted += projectWIP.completed || 0;
                projectCount++;
              }
            });
            
            if (projectCount > 0) {
              parentProjectMap[parentId].yetToStart = totalYetToStart / projectCount;
              parentProjectMap[parentId].inProgress = totalInProgress / projectCount;
              parentProjectMap[parentId].completed = totalCompleted / projectCount;
              parentProjectMap[parentId].count = projectCount;
            }
          });
          
          // Build table content
          let tableContent = '';
          
          if (Object.keys(parentProjectMap).length === 0) {
            tableContent = `
              <tr>
                <td colspan="7" class="text-center">
                  <i class="fas fa-info-circle"></i> No parent-child project relationships found.
                </td>
              </tr>
            `;
          } else {
            // Sort parent projects by ID
            const sortedParentIds = Object.keys(parentProjectMap).sort();
            
            sortedParentIds.forEach(parentId => {
              const parentData = parentProjectMap[parentId];
              const projectDetails = projectsMap[parentId] || { 
                name: "Unknown Project", 
                manager: "Not Assigned" 
              };
              
              const childProjectsDisplay = parentData.childProjectDetails.length > 0 
                ? parentData.childProjectDetails
                    .map(child => `${child.id} (${child.name})`)
                    .join('<br>')
                : "No child projects";
              
              if (parentData.count > 0 || parentData.childProjectDetails.length > 0) {
                tableContent += `
                  <tr>
                    <td>${parentId}</td>
                    <td>${projectDetails.name}</td>
                    <td>${projectDetails.manager}</td>
                    <td>${childProjectsDisplay}</td>
                    <td>${parentData.yetToStart.toFixed(2)}%</td>
                    <td>${parentData.inProgress.toFixed(2)}%</td>
                    <td>${parentData.completed.toFixed(2)}%</td>
                  </tr>
                `;
              }
            });
          }
          
          // Update table content
          tableBody.innerHTML = tableContent;
          
        } catch (error) {
          console.error("Error loading parent project WIP report:", error);
          tableBody.innerHTML = `
            <tr>
              <td colspan="7" class="text-center text-danger">
                <i class="fas fa-exclamation-triangle"></i> 
                Error loading parent project WIP data. Please try again.
              </td>
            </tr>
          `;
        }
      }

      document.getElementById("export-button").addEventListener("click", function () {
        try {
          // Create a new workbook
          const workbook = XLSX.utils.book_new();
          
          // Function to manually create a worksheet with styled headers
          function createStyledWorksheet(tableId) {
            const table = document.getElementById(tableId);
            const headers = [];
            const data = [];
            
            // Extract headers
            const headerRow = table.querySelector('thead tr');
            if (headerRow) {
              headerRow.querySelectorAll('th').forEach(th => {
                headers.push(th.textContent.trim());
              });
            }
            
            // Extract data
            table.querySelectorAll('tbody tr').forEach(tr => {
              const rowData = [];
              tr.querySelectorAll('td').forEach(td => {
                rowData.push(td.textContent.trim());
              });
              if (rowData.length > 0) {
                data.push(rowData);
              }
            });
            
            // Create worksheet
            const ws = XLSX.utils.aoa_to_sheet([headers, ...data]);
            
            // Add style to header row
            const range = XLSX.utils.decode_range(ws['!ref']);
            const headerStyle = { fill: { fgColor: { rgb: "FFFF00" }, patternType: 'solid' }, font: { bold: true } };
            
            for (let col = range.s.c; col <= range.e.c; col++) {
              const cellRef = XLSX.utils.encode_cell({ r: 0, c: col });
              if (!ws[cellRef]) continue;
              ws[cellRef].s = headerStyle;
            }
            
            return ws;
          }
          
          // Create worksheets with styled headers
          const cumulativeWorksheet = createStyledWorksheet("cumulative-wip-table");
          const detailedWorksheet = createStyledWorksheet("wip-report-table");
          const parentWorksheet = createStyledWorksheet("parent-wip-table");
          
          // Add worksheets to workbook
          XLSX.utils.book_append_sheet(workbook, cumulativeWorksheet, "Cumulative WIP");
          XLSX.utils.book_append_sheet(workbook, detailedWorksheet, "Detailed WIP");
          XLSX.utils.book_append_sheet(workbook, parentWorksheet, "Parent Project WIP");
          
          // Get the current date and time in IST (Indian Standard Time)
          const now = new Date();
          const istOffset = 5.5 * 60 * 60 * 1000; // IST is UTC+5:30
          const istDate = new Date(now.getTime() + istOffset);
          const currentDateTime = istDate.toISOString().replace("T", "_").split(".")[0].replace(/:/g, "-");
          
          // Generate the filename with the current date and time in IST
          const filename = `WIP_Reports_${currentDateTime}.xlsx`;
          
          // Export the workbook to an Excel file
          XLSX.writeFile(workbook, filename);
          
          // Show success message
          alert("Excel file with all reports has been downloaded.");
        } catch (error) {
          console.error("Error exporting to Excel:", error);
          alert("Error exporting to Excel. Please try again.");
        }
      });
      
    </script>
    <script>
      // Add this function to update user info including avatar
      function updateUserInterface() {
          const userRole = localStorage.getItem('role');
          const userName = localStorage.getItem('username');
          const userAvatar = localStorage.getItem('userAvatar');
          
          // Update username and role
          const userNameElement = document.querySelector('.user-name');
          const userRoleElement = document.querySelector('.user-role');
          if (userNameElement) userNameElement.textContent = `Welcome, ${userName || 'User'}`;
          if (userRoleElement) userRoleElement.textContent = userRole || 'Role';
          
          // Update all user photos on the page
          const userPhotos = document.querySelectorAll('.user-photo');
          userPhotos.forEach(photo => {
              if (userAvatar) {
                  photo.src = userAvatar;
              } else {
                  photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'; // Default photo
              }
          });
      }
      
      // Call this function when the page loads
      document.addEventListener('DOMContentLoaded', updateUserInterface);
      
      // Optional: Refresh user interface periodically to catch any changes
      setInterval(updateUserInterface, 30000); // Update every 30 seconds

      function goHome() {
  window.location.href = "index.html";
}

      function logout() {
            // Clear all user-related data
            const itemsToClear = ['token', 'username', 'role', 'userAvatar', 'userId'];
            itemsToClear.forEach(item => localStorage.removeItem(item));
            
            // Reset any user photos to default before redirecting
            const userPhotos = document.querySelectorAll('.user-photo');
            userPhotos.forEach(photo => {
                photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
            });
            
            // Redirect to login page
            window.location.href = '/login.html';
        }

// Reusable Fetch Wrapper with Authentication
async function fetchWithAuth(url, options = {}) {
  const token = localStorage.getItem("token");
  
  if (!token) {
    alert("Unauthorized access. Please log in.");
    window.location.href = "/login.html";
    throw new Error("No token found");
  }

  const headers = {
    ...options.headers,
    Authorization: `Bearer ${token}`,
  };

  const fetchOptions = {
    ...options,
    headers,
  };

  const response = await fetch(url, fetchOptions);

  if (response.status === 401 || response.status === 403) {
    alert("Session expired or unauthorized. Please log in again.");
    window.location.href = "/login.html";
    throw new Error("Unauthorized access");
  }

  return response;
}

// Check authentication when page loads
document.addEventListener('DOMContentLoaded', () => {
  const token = localStorage.getItem('token');
  
  if (!token) {
    alert('Unauthorized access. Please login again.');
    window.location.href = '/login.html';
    return;
  }
  
  // Load all reports
  loadWIPReport();
  loadCumulativeWIPReport();
  loadParentProjectWIPReport();
  
  // Update user interface
  updateUserInterface();
});

// Helper function to fetch project details from both collections
async function fetchProjectDetails() {
  try {
    // Fetch in-progress projects
    const inProgressResponse = await fetchWithAuth("/api/inprogressprojects");
    let inProgressProjects = [];
    if (inProgressResponse.ok) {
      inProgressProjects = await inProgressResponse.json();
      console.log("In-Progress Projects:", inProgressProjects);
    } else {
      console.error("Failed to fetch in-progress projects");
    }
    
    // Fetch completed projects
    const completedResponse = await fetchWithAuth("/api/completedprojects");
    let completedProjects = [];
    if (completedResponse.ok) {
      completedProjects = await completedResponse.json();
      console.log("Completed Projects:", completedProjects);
    } else {
      console.error("Failed to fetch completed projects");
    }
    
    // Combine both sets of projects
    const allProjects = [...inProgressProjects, ...completedProjects];
    
    // Create a map for quick lookup
    const projectsMap = {};
    allProjects.forEach(project => {
      projectsMap[project._id] = {
        name: project.title || "Unknown",
        manager: project.projectManager || "Not Assigned"
      };
    });
    
    console.log("Combined Projects Map:", projectsMap);
    return projectsMap;
  } catch (error) {
    console.error("Error fetching project details:", error);
    return {}; // Return empty map in case of error
  }
}

async function fetchWithSpinner(tbody, colSpan, url, buildRowsFn, loadingMsg){
  tbody.innerHTML = spinnerHTML(colSpan, loadingMsg);
  try{
    const data = await fetchWithAuth(url).then(r=>r.json());
    tbody.innerHTML = buildRowsFn(data);
  }catch(e){
    console.error(e);
    tbody.innerHTML = errorHTML(colSpan,'Error loading data');
  }
}

    </script>
</body>
</html>



