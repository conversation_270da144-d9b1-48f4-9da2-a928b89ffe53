<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tickets Dashboard</title>
    <link rel="stylesheet" href="styles/style.css">
    <link rel="stylesheet" href="styles/tickets.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <!-- Add Quill CSS and JS -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3/build/global/luxon.min.js"></script>
    
    <style>
      /* Notification Toast - Same as index.html */
      .toast-notification {
          position: fixed;
          bottom: 20px;
          right: 20px;
          background-color: #28a745;
          color: white;
          padding: 15px 20px;
          border-radius: 5px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          z-index: 1050;
          opacity: 0;
          visibility: hidden;
          transition: opacity 0.3s, visibility 0.3s, transform 0.3s;
          transform: translateY(20px);
      }
      .toast-notification.show {
          opacity: 1;
          visibility: visible;
          transform: translateY(0);
      }
    </style>
</head>
<body>
    <div class="toast-notification" id="toast-notification"></div>
    <div class="container-fluid"> 
        <div class="row">          
          <div class="main-wrapper-tickets">
            <header class="top-bar-tickets">
              <div class="left-section">
              <button onclick="goHome()" class="home-button">
                <i class="fas fa-home"></i> Home
              </button>
              <!-- Add the dropdowns here -->
            <div class="dropdown-container">
                <div class="dropdown">
                    <button class="dropdown-button">Production</button>
                    <div class="dropdown-content">
                      <a href="Move-to-Production.html">Move to Production</a>
                    </div>
                  </div>
                  <div class="dropdown">
                    <button class="dropdown-button">Reports</button>
                    <div class="dropdown-content">
                      <a href="WIP-report.html">WIP Report</a>
                    </div>
              </div>
              <div class="dropdown">
                <button class="dropdown-button">Project Management</button>
                <div class="dropdown-content">
                  <a href="/Invoices.html">Invoice</a>
                </div>
              </div>
              <div class="dropdown">
                <button class="dropdown-button">Tickets</button>
                <div class="dropdown-content">
                  <a href="/Tickets.html">Tickets</a>
                </div>
              </div>
            </div>
          </div>
            <!-- End the dropdowns here -->
            <div class="right-section">
              <div class="user-info">
                <div class="user-photo-container">
                  <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp" alt="User Photo" class="user-photo" onerror="this.onerror=null;this.src='https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';" />
                  <div class="user-dropdown-content">
                    <a href="/user-info.html" onclick="openSettings()"><i class="fas fa-cog"></i> Settings</a>
                  </div>
                </div>
                <div class="user-details">
                  <span class="user-name">Welcome, User</span>
                  <span class="user-role">Role</span>
                </div>
              </div>
                <button onclick="logout()" class="logout-button">Logout</button>
              </div>
            </header>

                <!-- Main Content -->
                <div class="tickets-dashboard">
                    <div class="dashboard-layout">
                        <!-- Left Side Content -->
                        <div class="left-content">
                            <!-- Statistics Cards -->
                            <div class="statistics-row">
                                <div class="stat-card">
                                    <div class="stat-icon bg-primary">
                                        <i class="fas fa-ticket-alt"></i>
                                    </div>
                                    <div class="stat-details">
                                        <h3>Total Tickets</h3>
                                        <span class="number" id="total-tickets">0</span>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon bg-success">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="stat-details">
                                        <h3>Resolved</h3>
                                        <span class="number" id="resolved-tickets">0</span>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon bg-warning">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="stat-details">
                                        <h3>Pending</h3>
                                        <span class="number" id="pending-tickets">0</span>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon bg-danger">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="stat-details">
                                        <h3>Over Due</h3>
                                        <span class="number" id="overdue-tickets">0</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Main Tickets Content -->
                            <div class="tickets-main-content">
                                <div class="tickets-section">
                                    <!-- Form Section -->
                                    <div class="ticket-form-section" id="ticketFormSection" style="display: none;">
                                        <div class="section-header">
                                            <h2>Create New Ticket</h2>
                                            <button class="btn btn-secondary" onclick="toggleTicketForm()">
                                                <i class="fas fa-times"></i> Close Form
                                            </button>
                                        </div>
                                        <form id="newTicketForm" class="ticket-form">
                                            <div class="row">
                                                <!-- Left Column -->
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="projectId">Project ID</label>
                                                        <input type="text" class="form-control" id="projectId" name="projectId" required>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="customerCode">Customer Code</label>
                                                        <input type="text" class="form-control" id="customerCode" name="customerCode" required>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="projectName">Project Name</label>
                                                        <input type="text" class="form-control" id="projectName" name="projectName" required>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="ticketHeading">Ticket Heading</label>
                                                        <input type="text" class="form-control" id="ticketHeading" name="ticketHeading" required>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="ticketType">Ticket Type</label>
                                                        <select class="form-control" id="ticketType" name="ticketType" required>
                                                            <option value="First">First</option>
                                                            <option value="Cust Alt">Cust Alt</option>
                                                            <option value="Rework">Rework</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="status">Status</label>
                                                        <select class="form-control" id="status" name="status" required>
                                                            <option value="open">Open</option>
                                                            <option value="in-progress">In Progress</option>
                                                            <option value="resolved">Resolved</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <!-- Right Column -->
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="dueDate">Due Date</label>
                                                        <input type="date" class="form-control" id="dueDate" name="dueDate" placeholder="MM/DD/YYYY" required>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="projectManager">Project Manager</label>
                                                        <input type="text" class="form-control" id="projectManager" name="projectManager" required>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="assignedTo">Assigned To</label>
                                                        <select class="form-control" id="assignedTo" name="assignedTo" required>
                                                            <option value="">Select Creative Service Member</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group mb-3">
                                                        <label for="supportingTeam" class="form-label">Supporting Team</label>
                                                        <div class="supporting-team-dropdown dropdown"> <!-- Added Bootstrap's dropdown class -->
                                                            <button class="supporting-team-btn form-control d-flex justify-content-between align-items-center" 
                                                                    type="button" 
                                                                    id="supportingTeamDropdown" 
                                                                    data-bs-toggle="dropdown" 
                                                                    aria-expanded="false">
                                                                <span class="selected-text">Supporting Team Members</span>
                                                                <i class="fas fa-chevron-down"></i>
                                                            </button>
                                                            <div class="supporting-team-menu dropdown-menu w-100 p-3 shadow-sm" aria-labelledby="supportingTeamDropdown">
                                                                <!-- Added Bootstrap's dropdown-menu class -->
                                                                <div class="search-container mb-2">
                                                                    <div class="input-group">
                                                                        <span class="input-group-text">
                                                                            <i class="fas fa-search"></i>
                                                                        </span>
                                                                        <input type="text" 
                                                                               class="form-control" 
                                                                               id="supportingTeamSearch" 
                                                                               placeholder="Search team members..."
                                                                               onclick="event.stopPropagation()">
                                                                    </div>
                                                                </div>
                                                                <div id="supportingTeamOptions" 
                                                                     class="team-options-container"
                                                                     onclick="event.stopPropagation()">
                                                                    <!-- Options will be populated here -->
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div id="selectedMembersBox" class="selected-members-box">
                                                            <!-- Selected members will be displayed here -->
                                                        </div>
                                                        <input type="hidden" id="supportingTeam" name="supportingTeam">
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="instructions">Instructions</label>
                                                        <div id="instructions-editor" class="quill-editor"></div>
                                                        <input type="hidden" id="instructions" name="instructions">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-actions">
                                                <button type="submit" class="btn btn-primary">Create Ticket</button>
                                                <button type="reset" class="btn btn-secondary">Reset Form</button>
                                            </div>
                                        </form>
                                    </div>

                                    <!-- Tickets List Section -->
                                    <div class="tickets-management">
                                        <div class="tickets-header">
                                            <h2>Tickets Management</h2>
                                            <button class="btn btn-primary" onclick="toggleTicketForm()">
                                                <i class="fas fa-plus"></i> New Ticket
                                            </button>
                                        </div>

                                        <!-- Filters -->
                                        <div class="tickets-filters">
                                            <div class="search-box">
                                                <input type="text" placeholder="Search tickets..." id="ticket-search">
                                                <i class="fas fa-search"></i>
                                            </div>
                                            <div class="filter-group">
                                                <select id="status-filter">
                                                    <option value="">All Tickets</option>
                                                    <option value="open">Open</option>
                                                    <option value="in-progress">In Progress</option>
                                                    <option value="resolved">Resolved</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Tabs for Ticket Status -->
                                        <ul class="nav nav-tabs mb-3" id="ticketStatusTabs" role="tablist">
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link active" id="open-tab" data-bs-toggle="tab" data-bs-target="#openTickets" type="button" role="tab" aria-controls="openTickets" aria-selected="true">Open & In Progress</button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="resolved-tab" data-bs-toggle="tab" data-bs-target="#resolvedTickets" type="button" role="tab" aria-controls="resolvedTickets" aria-selected="false">Resolved</button>
                                            </li>
                                        </ul>
                                        <div class="tab-content" id="ticketStatusTabsContent">
                                            <div class="tab-pane fade show active" id="openTickets" role="tabpanel" aria-labelledby="open-tab">
                                                <table class="table">
                                                    <thead>
                                                        <tr>
                                                            <th>Ticket Number</th>
                                                            <th onclick="sortTable(1)">Project ID <i class="fas fa-sort"></i></th>
                                                            <th onclick="sortTable(2)">Customer Code <i class="fas fa-sort"></i></th>
                                                            <th onclick="sortTable(3)">Project Name <i class="fas fa-sort"></i></th>
                                                            <th onclick="sortTable(4)">Ticket Heading <i class="fas fa-sort"></i></th>
                                                            <th onclick="sortTable(5)">Ticket Type <i class="fas fa-sort"></i></th>
                                                            <th onclick="sortTable(6)">Status <i class="fas fa-sort"></i></th>
                                                            <th onclick="sortTable(7)">Created Date <i class="fas fa-sort"></i></th>
                                                            <th onclick="sortTable(8)">Due Date <i class="fas fa-sort"></i></th>
                                                            <th onclick="sortTable(9)">Project Manager <i class="fas fa-sort"></i></th>
                                                            <th onclick="sortTable(10)">Assigned To <i class="fas fa-sort"></i></th>
                                                            <th>Comments</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="openTicketsTableBody">
                                                        <!-- Open/In Progress tickets will be loaded here -->
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="tab-pane fade" id="resolvedTickets" role="tabpanel" aria-labelledby="resolved-tab">
                                                <table class="table">
                                                    <thead>
                                                        <tr>
                                                            <th>Ticket Number</th>
                                                            <th>Project ID</th>
                                                            <th>Customer Code</th>
                                                            <th>Project Name</th>
                                                            <th>Ticket Heading</th>
                                                            <th>Ticket Type</th>
                                                            <th>Status</th>
                                                            <th>Created Date</th>
                                                            <th>Due Date</th>
                                                            <th>Project Manager</th>
                                                            <th>Assigned To</th>
                                                            <th>Comments</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="resolvedTicketsTableBody">
                                                        <!-- Resolved tickets will be loaded here -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Side Content -->
                        <div class="upcoming-tickets-section">
                            <!-- Overdue Tickets Section moved to top -->
                            <div class="overdue-tickets-section">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">Overdue Tickets <span class="badge badge-danger" id="overdue-count">0</span></h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="overdue-tickets-list" class="overdue-tickets-list"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Today/Tomorrow Tickets Section moved below -->
                            <div class="card">
                                <div class="card-header">
                                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link active" id="today-tab" data-toggle="tab" href="#today" role="tab" aria-controls="today" aria-selected="true">
                                                Due Today
                                                <span class="badge badge-warning" id="today-count">0</span>
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="tomorrow-tab" data-toggle="tab" href="#tomorrow" role="tab" aria-controls="tomorrow" aria-selected="false">
                                                Due Tomorrow
                                                <span class="badge badge-tomorrow" id="tomorrow-count">0</span>
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="dayafter-tab" data-toggle="tab" href="#dayafter" role="tab" aria-controls="dayafter" aria-selected="false">
                                                Day After
                                                <span class="badge badge-secondary" id="dayafter-count">0</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="card-body">
                                    <div class="tab-content">
                                        <div class="tab-pane fade show active" id="today" role="tabpanel" aria-labelledby="today-tab">
                                            <div id="today-tickets" class="upcoming-tickets-list"></div>
                                        </div>
                                        <div class="tab-pane fade" id="tomorrow" role="tabpanel" aria-labelledby="tomorrow-tab">
                                            <div id="tomorrow-tickets" class="upcoming-tickets-list"></div>
                                        </div>
                                        <div class="tab-pane fade" id="dayafter" role="tabpanel" aria-labelledby="dayafter-tab">
                                            <div id="dayafter-tickets" class="upcoming-tickets-list"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Instructions Modal -->
    <div class="modal fade" id="instructionsModal" tabindex="-1" aria-labelledby="instructionsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="instructionsModalLabel">Instructions</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="instructionsText" class="formatted-content"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Ticket Modal -->
    <div class="modal fade" id="editTicketModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Ticket</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editTicketForm">
                        <input type="hidden" id="editTicketId">
                        <div class="form-group">
                            <label>Project ID</label>
                            <input type="text" class="form-control" name="projectId" id="editProjectId" required>
                        </div>
                        <div class="form-group">
                            <label>Customer Code</label>
                            <input type="text" class="form-control" name="customerCode" id="editCustomerCode" required>
                        </div>
                        <div class="form-group">
                            <label>Project Name</label>
                            <input type="text" class="form-control" name="projectName" id="editProjectName" required>
                        </div>
                        <div class="form-group">
                            <label>Ticket Heading</label>
                            <input type="text" class="form-control" name="ticketHeading" id="editTicketHeading" required>
                        </div>
                        <div class="form-group">
                            <label>Ticket Type</label>
                            <select class="form-control" name="ticketType" id="editTicketType" required>
                                <option value="First">First</option>
                                <option value="Cust Alt">Cust Alt</option>
                                <option value="Rework">Rework</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Status</label>
                            <select class="form-control" name="status" id="editStatus" required>
                                <option value="open">Open</option>
                                <option value="in-progress">In Progress</option>
                                <option value="resolved">Resolved</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Due Date</label>
                            <input type="date" class="form-control" name="dueDate" id="editDueDate" placeholder="MM/DD/YYYY" required>
                        </div>
                        <div class="form-group">
                            <label>Project Manager</label>
                            <input type="text" class="form-control" name="projectManager" id="editProjectManager" required>
                        </div>
                        <div class="form-group">
                            <label>Assigned To</label>
                            <input type="text" class="form-control" name="assignedTo" id="editAssignedTo" required>
                        </div>
                        <div class="form-group">
                            <label for="editInstructions">Instructions</label>
                            <div id="edit-instructions-editor" class="quill-editor"></div>
                            <input type="hidden" id="editInstructions" name="editInstructions">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="saveTicketChanges()">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Supporting Team Modal -->
    <div class="modal fade" id="supportingTeamModal" tabindex="-1" aria-labelledby="supportingTeamModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="supportingTeamModalLabel">Supporting Team Members</h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <input type="text" id="teamSearchInput" class="form-control" placeholder="Search for team members...">
                    </div>
                    <div class="supporting-team-dropdown">
                        <div class="team-options-container" id="supportingTeamModalOptions">
                            <!-- Team members will be populated here -->
                        </div>
                    </div>
                    <div id="selectedMembersBoxModal" class="selected-members-box mt-3">
                        <!-- Selected members will be displayed here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="saveSupportingTeam()">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
    

    <script>
        document.addEventListener("DOMContentLoaded", () => {
          const userNameElement = document.querySelector(".user-name");
          const userRoleElement = document.querySelector(".user-role");
        
          // Retrieve the user's role and name from localStorage
          const userRole = localStorage.getItem("role");
          const userName = localStorage.getItem("username"); // Retrieve the username from localStorage
        
          // Update the user profile section
          if (userNameElement) {
            userNameElement.textContent = `Welcome, ${userName || "User"}`;
          }
          if (userRoleElement) {
            userRoleElement.textContent = userRole || "Role";
          }
        
          console.log("User Role:", userRole); // Debugging
          console.log("User Name:", userName); // Debugging
        });
        </script>
    <script>
        // Add this function to handle authenticated fetches
        async function fetchWithAuth(url, options = {}) {
            const token = localStorage.getItem('token');
            if (!token) {
                alert('No authentication token found. Please log in again.');
                window.location.href = '/login.html';
                return;
            }

            const headers = {
                ...options.headers,
                'Authorization': `Bearer ${token}`
            };

            return fetch(url, { ...options, headers });
        }

        // Initialize statistics
function updateStatistics() {
    fetchWithAuth('/api/tickets/statistics')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            document.getElementById('total-tickets').textContent = data.total || 0;
            document.getElementById('resolved-tickets').textContent = data.resolved || 0;
            document.getElementById('pending-tickets').textContent = data.pending || 0;
            // document.getElementById('overdue-tickets').textContent = data.overdue || 0; // Removed to avoid double update
        })
        .catch(error => {
            console.error('Error fetching statistics:', error);
        });
}

// Load tickets
let allTickets = [];

function loadTickets() {
    fetchWithAuth('/api/tickets')
        .then(response => response.json())
        .then(async tickets => {
            allTickets = tickets;
            // Separate tickets by status
            const openTickets = tickets.filter(t => t.status === 'open' || t.status === 'in-progress');
            const resolvedTickets = tickets.filter(t => t.status === 'resolved');

            // Render open/in-progress tickets
            const openTableBody = document.getElementById('openTicketsTableBody');
            openTableBody.innerHTML = '';
            for (const ticket of openTickets) {
                try {
                    const row = await createTicketRow(ticket);
                    openTableBody.appendChild(row);
                } catch (error) {
                    console.error('Error creating ticket row:', error);
                }
            }

            // Render resolved tickets
            const resolvedTableBody = document.getElementById('resolvedTicketsTableBody');
            resolvedTableBody.innerHTML = '';
            for (const ticket of resolvedTickets) {
                try {
                    const row = await createTicketRow(ticket);
                    resolvedTableBody.appendChild(row);
                } catch (error) {
                    console.error('Error creating ticket row:', error);
                }
            }
        })
        .catch(error => {
            console.error('Error loading tickets:', error);
            document.getElementById('openTicketsTableBody').innerHTML = '<tr><td colspan="13" class="text-center">Error loading tickets</td></tr>';
            document.getElementById('resolvedTicketsTableBody').innerHTML = '<tr><td colspan="13" class="text-center">Error loading tickets</td></tr>';
        });
}

async function createTicketRow(ticket) {
    const row = document.createElement('tr');
    const userRole = localStorage.getItem('role');

    // Define action buttons based on user role
    let actionButtons = `
        <button class="btn btn-link" onclick="showInstructions('${ticket._id}')" title="Instructions">
            <i class="fas fa-info-circle text-info"></i>
        </button>
        <button class="btn btn-link" onclick="showSupportingTeam('${ticket._id}')" title="Supporting Team">
            <i class="fas fa-users text-success"></i>
        </button>
        <button class="btn btn-link" onclick="exportTicketPDF('${ticket._id}')" title="Export PDF">
            <i class="fas fa-file-pdf text-danger"></i>
        </button>
    `;

    if (userRole === 'Admin' || userRole === 'Project Manager') {
        actionButtons = `
            <button class="btn btn-link" onclick="editTicket('${ticket._id}')" title="Edit">
                <i class="fas fa-edit text-primary"></i>
            </button>
            <button class="btn btn-link" onclick="deleteTicket('${ticket._id}')" title="Delete">
                <i class="fas fa-trash text-danger"></i>
            </button>
        ` + actionButtons;
    }

    row.innerHTML = `
        <td>${ticket.ticketNumber || ''}</td>
        <td>${ticket.projectId || ''}</td>
        <td>${ticket.customerCode || ''}</td>
        <td>${ticket.projectName || ''}</td>
        <td>${ticket.ticketHeading || ''}</td>
        <td>${ticket.ticketType || ''}</td>
        <td>
            <select class="status-select" data-ticket-id="${ticket._id}" onchange="updateTicketStatus(this)">
                <option value="open" ${ticket.status === 'open' ? 'selected' : ''}>Open</option>
                <option value="in-progress" ${ticket.status === 'in-progress' ? 'selected' : ''}>In Progress</option>
                <option value="resolved" ${ticket.status === 'resolved' ? 'selected' : ''}>Resolved</option>
            </select>
        </td>
        <td>${formatDateCST(ticket.createdDate)}</td>
        <td>${formatDateCST(ticket.dueDate)}</td>
        <td>${ticket.projectManager || ''}</td>
        <td>${ticket.assignedTo || ''}</td>
        <td>
            <div class="comments-section">
                <textarea class="form-control comments-input" 
                          data-ticket-id="${ticket._id}" 
                          placeholder="Add a comment..."
                          onblur="updateTicketComment(this)">${ticket.comments || ''}</textarea>
            </div>
        </td>
        <td class="action-buttons">
            ${actionButtons}
        </td>
    `;

    // Initialize the status style
    const statusSelect = row.querySelector('.status-select');
    updateStatusStyle(statusSelect);
    
    // Store the current value as previous value for reverting if update fails
    statusSelect.setAttribute('data-previous-value', ticket.status);

    // Add event listener for the comments textarea
    const commentsTextarea = row.querySelector('.comments-input');
    commentsTextarea.addEventListener('blur', function() {
        updateTicketComment(this);
    });

    return row;
}

// Helper function to format dates in MM/DD/YYYY format
function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';
    
    // Format as MM/DD/YYYY
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear();
    
    return `${month}/${day}/${year}`;
}

// Helper function to get status badge class
function getStatusBadgeClass(status) {
    switch (status?.toLowerCase()) {
        case 'open':
            return 'warning';
        case 'in-progress':
            return 'primary';
        case 'resolved':
            return 'success';
        default:
            return 'secondary';
    }
}

// Search functionality
document.getElementById('ticket-search').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const rows = document.querySelectorAll('#tickets-list tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});

// Filter functionality
function applyFilters() {
    const status = document.getElementById('status-filter').value.toLowerCase();
    const searchTerm = document.getElementById('ticket-search').value.toLowerCase();

    // Only filter rows in the currently visible tab
    const openTabActive = document.getElementById('openTickets').classList.contains('show') && document.getElementById('openTickets').classList.contains('active');
    const resolvedTabActive = document.getElementById('resolvedTickets').classList.contains('show') && document.getElementById('resolvedTickets').classList.contains('active');

    if (openTabActive) {
        const rows = document.querySelectorAll('#openTicketsTableBody tr');
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const statusCell = row.querySelector('.status-select')?.value.toLowerCase();
            const matchesStatus = !status || statusCell === status;
            const matchesSearch = !searchTerm || text.includes(searchTerm);
            row.style.display = matchesStatus && matchesSearch ? '' : 'none';
        });
    } else if (resolvedTabActive) {
        const rows = document.querySelectorAll('#resolvedTicketsTableBody tr');
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            // For resolved, status is always 'resolved', so just filter by search
            const matchesSearch = !searchTerm || text.includes(searchTerm);
            row.style.display = matchesSearch ? '' : 'none';
        });
    }
}

// Add event listeners for filters
document.addEventListener('DOMContentLoaded', () => {
    const statusFilter = document.getElementById('status-filter');
    const searchInput = document.getElementById('ticket-search');
    
    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }
    
    if (searchInput) {
        searchInput.addEventListener('input', applyFilters);
    }
});

// Toggle ticket form visibility
function toggleTicketForm() {
    const formSection = document.getElementById('ticketFormSection');
    formSection.style.display = formSection.style.display === 'none' ? 'block' : 'none';
}

// Handle form submission
document.getElementById('newTicketForm').addEventListener('submit', async function(event) {
    event.preventDefault();
    const submitButton = this.querySelector('button[type="submit"]');
    submitButton.disabled = true;
    submitButton.textContent = 'Creating...';
    
    try {
        // Get content from Quill editor
        const instructions = document.getElementById('instructions').value;
        
        // Get supporting team members
        const supportingTeam = Array.from(
            document.querySelectorAll('#selectedMembersBox .selected-member')
        ).map(el => el.dataset.username);
    
        const newTicket = {
            projectId: document.getElementById('projectId').value,
            customerCode: document.getElementById('customerCode').value,
            projectName: document.getElementById('projectName').value,
            ticketHeading: document.getElementById('ticketHeading').value,
            ticketType: document.getElementById('ticketType').value,
            status: document.getElementById('status').value,
            dueDate: getChicagoISOStringFromDateInput(document.getElementById('dueDate').value),
            projectManager: document.getElementById('projectManager').value,
            assignedTo: document.getElementById('assignedTo').value,
            supportingTeam: supportingTeam,
            instructions: instructions
    };

        const response = await fetchWithAuth('/api/tickets', {
        method: 'POST',
        headers: {
                'Content-Type': 'application/json'
        },
            body: JSON.stringify(newTicket)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const createdTicket = await response.json();
        console.log('Ticket created successfully:', createdTicket);

        // Reset form
        document.getElementById('newTicketForm').reset();
        
        // Clear Quill editor
        if (newTicketQuill) {
            newTicketQuill.root.innerHTML = '';
        }
        
        // Clear selected team members
        document.getElementById('selectedMembersBox').innerHTML = '';
        document.getElementById('supportingTeam').value = '';
        
        // Hide form
        toggleTicketForm();
        
        // Redirect to tickets page with success parameter
        window.location.href = '/Tickets.html?ticket_created=true';
    } catch (error) {
        console.error('Error creating ticket:', error);
        showAlert('Error creating ticket: ' + error.message, 'danger');
    } finally {
        submitButton.disabled = false;
        submitButton.textContent = 'Create Ticket';
    }
});

// Function to format date for input fields (YYYY-MM-DD format required by HTML date inputs)
function formatDateForInput(dateString) {
    if (!dateString) return '';
    
    // Create a new Date object from the date string
    const date = new Date(dateString);
    
    // Check if the date is valid
    if (isNaN(date.getTime())) return '';
    
    // Format the date as YYYY-MM-DD
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
}

// Edit Ticket Function
async function editTicket(ticketId) {
    const ticket = allTickets.find(t => t._id === ticketId);
    if (ticket) {
        // Store the instructions content in a data attribute for reliable access
        document.getElementById('editTicketModal').setAttribute('data-instructions', ticket.instructions || '');
        
        // Fill in the form fields
        document.getElementById('editTicketId').value = ticket._id;
        document.getElementById('editProjectId').value = ticket.projectId;
        document.getElementById('editCustomerCode').value = ticket.customerCode;
        document.getElementById('editProjectName').value = ticket.projectName;
        document.getElementById('editTicketHeading').value = ticket.ticketHeading;
        document.getElementById('editTicketType').value = ticket.ticketType;
        document.getElementById('editStatus').value = ticket.status;
        document.getElementById('editDueDate').value = formatDateForInput(ticket.dueDate);
        document.getElementById('editProjectManager').value = ticket.projectManager;
        document.getElementById('editAssignedTo').value = ticket.assignedTo;
        
        // Show the modal using Bootstrap 5 API
        const modal = new bootstrap.Modal(document.getElementById('editTicketModal'));
        modal.show();
        
        // Set the instructions content in Quill after a short delay to ensure Quill is ready
        setTimeout(() => {
            if (editTicketQuill) {
                editTicketQuill.root.innerHTML = ticket.instructions || '';
                document.getElementById('editInstructions').value = ticket.instructions || '';
            }
        }, 100);
    }
}

// Save Ticket Changes
async function saveTicketChanges() {
    console.log('Save Changes clicked');
    try {
        const ticketId = document.getElementById('editTicketId').value;
        
        // Get content from Quill editor
        const instructions = document.getElementById('editInstructions').value;

        const ticketData = {
            projectId: document.getElementById('editProjectId').value,
            customerCode: document.getElementById('editCustomerCode').value,
            projectName: document.getElementById('editProjectName').value,
            ticketHeading: document.getElementById('editTicketHeading').value,
            ticketType: document.getElementById('editTicketType').value,
            status: document.getElementById('editStatus').value,
            dueDate: getChicagoISOStringFromDateInput(document.getElementById('editDueDate').value),
            projectManager: document.getElementById('editProjectManager').value,
            assignedTo: document.getElementById('editAssignedTo').value,
            instructions: instructions
        };

        const response = await fetchWithAuth(`/api/tickets/${ticketId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(ticketData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const updatedTicket = await response.json();
        console.log('Ticket updated successfully:', updatedTicket);

        // Close the modal using Bootstrap 5 method
        const modal = bootstrap.Modal.getInstance(document.getElementById('editTicketModal'));
        modal.hide();

        // Refresh the tickets list
        await loadTickets();
        await updateStatistics();
        
        // Show success message
        showAlert('Ticket updated successfully', 'success');
    } catch (error) {
        console.error('Error updating ticket:', error);
        showAlert('Error updating ticket: ' + error.message, 'danger');
    }
}

// Add the PDF export function
async function exportTicketPDF(ticketId) {
    try {
        const response = await fetchWithAuth(`/api/tickets/${ticketId}/pdf`, {
            method: 'GET',
            headers: {
                'Accept': 'application/pdf'
            }
        });

        if (!response.ok) throw new Error('Failed to generate PDF');

        // Get the filename from the Content-Disposition header if available
        let filename = 'ticket.pdf';
        const contentDisposition = response.headers.get('Content-Disposition');
        if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename=(.+)$/);
            if (filenameMatch && filenameMatch[1]) {
                filename = filenameMatch[1].replace(/"/g, '');
            }
        }
        
        // Create blob from response
        const blob = await response.blob();
        
        // Create download link
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        
        // Trigger download
        document.body.appendChild(a);
        a.click();
        
        // Cleanup
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    } catch (error) {
        console.error('Error exporting PDF:', error);
        alert('Error generating PDF');
    }
}

// Soft Delete Ticket (mark as deleted)
async function deleteTicket(ticketId) {
    if (!confirm('Are you sure you want to delete this ticket? It will be marked as deleted but kept in the database for reference.')) return;

    try {
        const response = await fetchWithAuth(`/api/tickets/${ticketId}`, {
            method: 'DELETE'
        });

        if (!response.ok) throw new Error('Failed to delete ticket');

        loadTickets();
        updateStatistics();
        alert('Ticket deleted successfully');
    } catch (error) {
        console.error('Error deleting ticket:', error);
        alert('Error deleting ticket');
    }
}

function showInstructions(ticketId) {
    fetchWithAuth(`/api/tickets/${ticketId}`)
        .then(response => response.json())
        .then(ticket => {
            // Use innerHTML instead of textContent to preserve formatting
            document.getElementById('instructionsText').innerHTML = ticket.instructions || 'No instructions available';
            $('#instructionsModal').modal('show');
        })
        .catch(error => {
            console.error('Error fetching instructions:', error);
            alert('Error fetching instructions');
        });
}

// Add the status update function
async function updateTicketStatus(selectElement) {
    const ticketId = selectElement.dataset.ticketId;
    const newStatus = selectElement.value.toLowerCase(); // Ensure lowercase status

    try {
        const response = await fetchWithAuth(`/api/tickets/${ticketId}/status`, {
            method: 'PATCH', // Changed from PUT to PATCH to match backend
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status: newStatus })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        updateStatusStyle(selectElement);
        // Refresh the tickets display to update counts
        loadTickets();
        updateStatistics();
        loadUpcomingTickets();
    } catch (error) {
        console.error('Error updating ticket status:', error);
        alert('Error updating ticket status');
        // Revert the select element to its previous value
        selectElement.value = selectElement.getAttribute('data-previous-value');
    }
}

// Add function to update status styling
function updateStatusStyle(selectElement) {
    const status = selectElement.value;
    
    // Reset background and text color
    selectElement.style.backgroundColor = '';
    selectElement.style.color = '';
    
    switch(status) {
        case 'open':
            selectElement.style.backgroundColor = '#fff3cd';
            selectElement.style.color = '#856404';
            break;
        case 'in-progress':
            selectElement.style.backgroundColor = '#cce5ff';
            selectElement.style.color = '#004085';
            break;
        case 'resolved':
            selectElement.style.backgroundColor = '#d4edda';
            selectElement.style.color = '#155724';
            break;
        case 'deleted':
            selectElement.style.backgroundColor = '#f8f9fa';
            selectElement.style.color = '#6c757d';
            break;
    }
}

// Add this function to render the filtered tickets
function renderTickets(tickets) {
    const ticketsTableBody = document.getElementById('ticketsTableBody');
    ticketsTableBody.innerHTML = '';

    tickets.forEach(ticket => {
        const row = createTicketRow(ticket);
        ticketsTableBody.appendChild(row);
    });
}

// Initialize page
document.addEventListener('DOMContentLoaded', () => {
    const token = localStorage.getItem('token');
    if (!token) {
        alert('Please log in to access this page');
        window.location.href = '/login.html';
        return;
    }
    
    updateStatistics();
    loadTickets();
}); // Removed event listeners for filters

// Navigation functions
function goHome() {
  window.location.href = "index.html";
}

function logout() {
            // Clear all user-related data
            const itemsToClear = ['token', 'username', 'role', 'userAvatar', 'userId'];
            itemsToClear.forEach(item => localStorage.removeItem(item));
            
            // Reset ALL user photos to default before redirecting
            const userPhotos = document.querySelectorAll('.user-photo, #userAvatar');
            userPhotos.forEach(photo => {
                photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
            });
            
            // Redirect to login page
            window.location.href = '/login.html';
        }

// Initialize Quill editors
let newTicketQuill, editTicketQuill;

function initializeQuillEditors() {
    // Initialize the editor for new tickets
    if (document.getElementById('instructions-editor')) {
        newTicketQuill = new Quill('#instructions-editor', {
            theme: 'snow',
            modules: {
                toolbar: [
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                    ['link', 'image'],
                    ['clean']
                ]
            },
            placeholder: 'Write instructions here...'
        });
        
        // Update hidden input when editor content changes
        newTicketQuill.on('text-change', function() {
            document.getElementById('instructions').value = newTicketQuill.root.innerHTML;
        });
    }
}

// Initialize Quill editor for the edit modal when it's shown
$('#editTicketModal').on('shown.bs.modal', function () {
    const instructions = this.getAttribute('data-instructions') || '';
    console.log('Loading instructions into editor:', instructions.substring(0, 50) + '...');
    
    // Initialize Quill if it doesn't exist
    if (!editTicketQuill) {
        console.log('Initializing Quill editor');
        editTicketQuill = new Quill('#edit-instructions-editor', {
            theme: 'snow',
            modules: {
                toolbar: [
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                    ['link', 'image'],
                    ['clean']
                ]
            },
            placeholder: 'Write instructions here...'
        });
        
        // Update hidden input when editor content changes
        editTicketQuill.on('text-change', function() {
            document.getElementById('editInstructions').value = editTicketQuill.root.innerHTML;
        });
    }
    
    // Set content with a more reliable approach using a short delay
    setTimeout(() => {
        if (editTicketQuill) {
            console.log('Setting Quill content');
            editTicketQuill.root.innerHTML = instructions;
            document.getElementById('editInstructions').value = instructions;
            
            // Ensure the editor container has proper height
            const editorContainer = document.querySelector('#edit-instructions-editor');
            if (editorContainer) {
                // Make sure the editor is visible and scrollable
                editorContainer.style.maxHeight = '350px';
                editorContainer.style.overflowY = 'auto';
                
                // Also ensure the editor content area is scrollable
                const editorContent = editorContainer.querySelector('.ql-editor');
                if (editorContent) {
                    editorContent.style.maxHeight = '300px';
                    editorContent.style.overflowY = 'auto';
                }
            }
        } else {
            console.error('Quill editor not initialized');
        }
    }, 200);
});

// Add a reset function when the modal is hidden
$('#editTicketModal').on('hidden.bs.modal', function () {
    // Clear the data attribute
    this.removeAttribute('data-instructions');
    
    // Optionally destroy and recreate the editor on next open for a fresh start
    if (editTicketQuill) {
        console.log('Clearing Quill editor content');
        editTicketQuill.root.innerHTML = '';
        document.getElementById('editInstructions').value = '';
    }
});

// Initialize editors when document is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeQuillEditors();
});

// Function to load upcoming tickets
async function loadUpcomingTickets() {
    try {
        const { DateTime } = luxon;
        // Get 'today', 'tomorrow', 'day after' in America/Chicago
        const nowChicago = DateTime.now().setZone('America/Chicago').startOf('day');
        const tomorrowChicago = nowChicago.plus({ days: 1 });
        const dayAfterChicago = nowChicago.plus({ days: 2 });

        const response = await fetchWithAuth('/api/tickets');
        const tickets = await response.json();

        const todayTickets = tickets.filter(ticket => {
            const dueChicago = DateTime.fromISO(ticket.dueDate, { zone: 'utc' }).setZone('America/Chicago').startOf('day');
            return dueChicago.equals(nowChicago);
        });

        const tomorrowTickets = tickets.filter(ticket => {
            const dueChicago = DateTime.fromISO(ticket.dueDate, { zone: 'utc' }).setZone('America/Chicago').startOf('day');
            return dueChicago.equals(tomorrowChicago);
        });

        const dayAfterTickets = tickets.filter(ticket => {
            const dueChicago = DateTime.fromISO(ticket.dueDate, { zone: 'utc' }).setZone('America/Chicago').startOf('day');
            return dueChicago.equals(dayAfterChicago);
        });

        const overdueTickets = tickets.filter(ticket => {
            const dueChicago = DateTime.fromISO(ticket.dueDate, { zone: 'utc' }).setZone('America/Chicago').startOf('day');
            const status = ticket.status.toLowerCase();
            return dueChicago < nowChicago && status !== 'resolved' && status !== 'closed';
        });

        // Update all overdue counts
        document.getElementById('overdue-tickets').textContent = overdueTickets.length; // Stat card count
        document.getElementById('overdue-count').textContent = overdueTickets.length;   // Badge count
        
        // Update other counts
        document.getElementById('today-count').textContent = todayTickets.length;
        document.getElementById('tomorrow-count').textContent = tomorrowTickets.length;
        document.getElementById('dayafter-count').textContent = dayAfterTickets.length;

        // Render tickets
        renderUpcomingTickets('today-tickets', todayTickets);
        renderUpcomingTickets('tomorrow-tickets', tomorrowTickets);
        renderUpcomingTickets('dayafter-tickets', dayAfterTickets);
        renderOverdueTickets(overdueTickets);
    } catch (error) {
        console.error('Error loading upcoming tickets:', error);
        ['today-tickets', 'tomorrow-tickets', 'dayafter-tickets', 'overdue-tickets-list'].forEach(containerId => {
            document.getElementById(containerId).innerHTML = 
                '<div class="alert alert-danger">Error loading tickets</div>';
        });
    }
}

// Function to render upcoming tickets
function renderUpcomingTickets(containerId, tickets) {
    const container = document.getElementById(containerId);
    if (!tickets || tickets.length === 0) {
        container.innerHTML = '<div class="no-tickets">No tickets scheduled</div>';
        return;
    }
    container.innerHTML = '';
    tickets.forEach(ticket => {
        const cardFlip = document.createElement('div');
        cardFlip.className = 'card-flip';
        // Use yellow side color for Due Today
        let sideClass = 'overdue-ticket-item';
        if (containerId === 'today-tickets') sideClass = 'due-today-ticket-item';
        else if (containerId === 'tomorrow-tickets') sideClass = 'due-tomorrow-ticket-item';
        cardFlip.innerHTML = `
          <div class="card-inner">
            <div class="card-front ${sideClass}">
              <div class="ticket-header">
                <span class="project-id">${ticket.projectId || ''}</span>
                <span class="ticket-status ${ticket.status ? ticket.status.toLowerCase() : ''}">${ticket.status || ''}</span>
              </div>
              <div class="ticket-project">${ticket.projectName || 'No Project Name'}</div>
              <div class="ticket-title">${ticket.ticketHeading || 'No Heading'}</div>
              <div class="ticket-meta mt-2">
                <div><i class="fas fa-user"></i> ${ticket.assignedTo || 'Unassigned'}</div>
                <div><i class="fas fa-calendar-alt"></i> Due: ${formatDateCST(ticket.dueDate)}</div>
              </div>
            </div>
            <div class="card-back ${sideClass}">
              <div><strong>Project ID:</strong> <span class="project-id">${ticket.projectId || ''}</span></div>
              <div><strong>Ticket Number:</strong> <span class="ticket-number">${ticket.ticketNumber || ''}</span></div>
              <div><strong>Comment:</strong> <span class="ticket-comment">${ticket.comments || ''}</span></div>
            </div>
          </div>
        `;
        cardFlip.addEventListener('click', function(e) {
          if (e.target.tagName.toLowerCase() === 'button') return;
          cardFlip.classList.toggle('flipped');
        });
        container.appendChild(cardFlip);
    });
}

// Helper function to format time
function formatTime(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

// Add these CSS styles for the updated ticket items
const style = document.createElement('style');
style.textContent = `
    .upcoming-tickets-container {
        display: flex;
        gap: 20px;
        margin: 20px 0;
    }

    .upcoming-tickets-section {
        flex: 1;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
    }

    .upcoming-tickets-section h3 {
        margin-bottom: 15px;
        color: #333;
        font-size: 1.2rem;
    }

    .upcoming-tickets-list {
        max-height: 500px;
        overflow-y: auto;
    }

    .upcoming-ticket-item {
        background: white;
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .ticket-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
    }

    .ticket-status {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
    }

    .ticket-status.open { background: #fff3cd; color: #856404; }
    .ticket-status.in-progress { background: #cce5ff; color: #004085; }
    .ticket-status.resolved { background: #d4edda; color: #155724; }

    .ticket-project, .ticket-title {
        margin-bottom: 5px;
        font-size: 0.9rem;
    }

    .ticket-meta {
        display: flex;
        justify-content: space-between;
        font-size: 0.8rem;
        color: #666;
    }

    .error-message {
        color: #dc3545;
        text-align: center;
        padding: 10px;
    }

    .no-tickets {
        text-align: center;
        color: #666;
        padding: 20px;
    }

    .upcoming-ticket-item {
      position: relative;
      cursor: pointer;
      transition: transform 0.6s;
      transform-style: preserve-3d;
    }
    
`;
document.head.appendChild(style);

// Tab switching functionality
document.addEventListener('DOMContentLoaded', () => {
    // Initialize Bootstrap tabs
    $('#today-tab, #tomorrow-tab, #dayafter-tab').on('click', function (e) {
        e.preventDefault();
        $(this).tab('show');
    });

    // Load upcoming tickets when page loads
    loadUpcomingTickets();
    
    // Refresh upcoming tickets every 5 minutes
    setInterval(loadUpcomingTickets, 300000);
});

function exportToPDF() {
    const doc = new jsPDF();
    
    // Add table headers
    const headers = [
        'Project ID', 'Customer Code', 'Project Name', 'Ticket Heading', 
        'Ticket Type', 'Status', 'Created Date', 'Due Date', 
        'Project Manager', 'Assigned To'
    ];
    
    // Transform tickets data
    const data = allTickets.map(ticket => [
        ticket.projectId,
        ticket.customerCode,
        ticket.projectName,
        ticket.ticketHeading,
        ticket.ticketType,
        ticket.status,
        formatDateCST(ticket.createdDate), // Use CST date-only
        formatDateCST(ticket.dueDate),     // Use CST date-only
        ticket.projectManager,
        ticket.assignedTo
    ]);

    // Generate PDF
    doc.autoTable({
        head: [headers],
        body: data,
        startY: 20,
        margin: { top: 20 },
        styles: { overflow: 'linebreak' },
        columnStyles: { text: { cellWidth: 'auto' } }
    });

    // Save the PDF
    doc.save('tickets-report.pdf');
}

// Add event listener to store previous value before change
document.addEventListener('DOMContentLoaded', () => {
    document.body.addEventListener('change', (e) => {
        if (e.target.classList.contains('status-select')) {
            e.target.setAttribute('data-previous-value', e.target.value);
        }
    });
});


// Add this script after your form
document.getElementById('projectId').addEventListener('input', async function(e) {
    const projectId = e.target.value.trim();
    
    if (projectId.length > 0) {
        try {
            const response = await fetchWithAuth(`/api/projects/${projectId}`);

            if (response.ok) {
                const projectData = await response.json();
                console.log("Project data loaded:", projectData); // Debug log
                
                // Auto-fill the fields
                document.getElementById('customerCode').value = projectData.clientName;
                document.getElementById('projectName').value = projectData.title;
                document.getElementById('projectManager').value = projectData.projectManager;
                
                // Set Assigned To (Creative Service Lead)
                if (projectData.creativeServiceLead) {
                    const assignedToSelect = document.getElementById('assignedTo');
                    if (assignedToSelect) {
                        assignedToSelect.value = projectData.creativeServiceLead;
                    }
                }

                // Auto-populate supporting team (supportingTeam + leads)
                const leadsToSelect = [
                    projectData.editorialLead,
                    projectData.creativeServiceLead,
                    projectData.qcLead,
                    projectData.digitalLead
                ].filter(Boolean); // Remove undefined/null

                setTimeout(() => {
                    // Clear any previously selected team members
                    const checkboxes = document.querySelectorAll('#supportingTeamOptions input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = false;
                    });

                    // Combine supportingTeam and leads
                    const allToSelect = new Set([
                        ...(projectData.supportingTeam || []),
                        ...leadsToSelect
                    ]);
                    allToSelect.forEach(member => {
                        const checkbox = document.querySelector(`#supportingTeamOptions input[data-username="${member}"]`);
                        if (checkbox) {
                            checkbox.checked = true;
                        }
                    });

                    // Manually update the selected members display
                    const selectedMembersBox = document.getElementById('selectedMembersBox');
                    const selectedUsers = Array.from(
                        document.querySelectorAll('#supportingTeamOptions input[type="checkbox"]:checked')
                    ).map(cb => ({
                        id: cb.value,
                        username: cb.dataset.username
                    }));

                    // Update hidden input
                    document.getElementById('supportingTeam').value = selectedUsers.map(user => user.id).join(',');

                    // Update the selected members box
                    selectedMembersBox.innerHTML = selectedUsers.map(user => `
                        <span class="selected-member-tag" data-id="${user.id}">
                            ${user.username}
                            <i class="fas fa-times remove-member" onclick="removeTeamMember('${user.id}')"></i>
                        </span>
                    `).join('');

                    console.log("Selected members updated:", selectedUsers); // Debug log
                }, 500); // Give time for users to load
            } else {
                // Clear fields if project not found
                document.getElementById('customerCode').value = '';
                document.getElementById('projectName').value = '';
                document.getElementById('projectManager').value = '';
                const assignedToSelect = document.getElementById('assignedTo');
                if (assignedToSelect) assignedToSelect.value = '';
            }
        } catch (error) {
            console.error('Error fetching project details:', error);
        }
    }
});

// Function to fetch and populate Creative Service users
async function loadCreativeServiceUsers() {
    try {
        const response = await fetchWithAuth('/api/users/creative-service');
        if (response.ok) {
            const users = await response.json();
            const assignedToSelect = document.getElementById('assignedTo');
            
            // Clear existing options (except the first default option)
            while (assignedToSelect.options.length > 1) {
                assignedToSelect.remove(1);
            }

            // Add users to dropdown - use username for both value and display
            users.forEach(user => {
                const option = new Option(user.username, user.username);
                assignedToSelect.add(option);
            });
        }
    } catch (error) {
        console.error('Error loading Creative Service users:', error);
    }
}

// Function to fetch and populate Supporting Team dropdown
async function loadAllUsers() {
    try {
        const response = await fetchWithAuth('/api/users/all');
        if (response.ok) {
            const users = await response.json();
            const supportingTeamOptions = document.getElementById('supportingTeamOptions');
            
            if (supportingTeamOptions) {
                supportingTeamOptions.innerHTML = users.map(user => `
                    <div class="form-check">
                        <input type="checkbox" 
                               class="form-check-input" 
                               id="user-${user._id}" 
                               value="${user._id}" 
                               data-username="${user.username}">
                        <label class="form-check-label" for="user-${user._id}">
                            ${user.username}
                        </label>
                    </div>
                `).join('');

                setupSupportingTeamHandlers();
            }
        }
    } catch (error) {
        console.error('Error loading users:', error);
    }
}

function setupSupportingTeamHandlers() {
    const dropdownButton = document.getElementById('supportingTeamDropdown');
    const searchInput = document.getElementById('supportingTeamSearch');
    const optionsContainer = document.getElementById('supportingTeamOptions');
    const hiddenInput = document.getElementById('supportingTeam');
    const selectedTextSpan = dropdownButton.querySelector('.selected-text');
    const selectedMembersBox = document.getElementById('selectedMembersBox');

    searchInput?.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        const options = optionsContainer.querySelectorAll('.form-check');
        options.forEach(option => {
            const text = option.textContent.trim().toLowerCase();
            option.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    optionsContainer?.addEventListener('change', (e) => {
        if (e.target.matches('input[type="checkbox"]')) {
            updateSelectedMembers();
        }
    });

    function updateSelectedMembers() {
        const checkboxes = optionsContainer.querySelectorAll('input[type="checkbox"]:checked');
        const selectedUsers = Array.from(checkboxes).map(cb => ({
            id: cb.value,
            username: cb.dataset.username
        }));

        hiddenInput.value = selectedUsers.map(user => user.id).join(',');
        selectedTextSpan.textContent = 'Supporting Team Members';
        
        // Update the selected members box
        selectedMembersBox.innerHTML = selectedUsers.map(user => `
            <span class="selected-member-tag" data-id="${user.id}">
                ${user.username}
                <i class="fas fa-times remove-member" onclick="removeTeamMember('${user.id}')"></i>
            </span>
        `).join('');
    }

    // Add this function to handle member removal
    window.removeTeamMember = function(userId) {
        const checkbox = document.getElementById(`user-${userId}`);
        if (checkbox) {
            checkbox.checked = false;
            updateSelectedMembers();
        }
    };
}

// Initialize when document loads
document.addEventListener('DOMContentLoaded', () => {
    loadCreativeServiceUsers();
    loadAllUsers();
});

// Add a function to show alerts
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Insert the alert at the top of the main content area
    const mainContent = document.querySelector('.main-content') || document.body;
    mainContent.insertBefore(alertDiv, mainContent.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Update the modal close functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get all close buttons in the edit modal
    const closeButtons = document.querySelectorAll('#editTicketModal .btn-secondary, #editTicketModal .close, #editTicketModal [data-dismiss="modal"]');
    
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Using Bootstrap 5 modal method
            const modal = document.getElementById('editTicketModal');
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            } else {
                // Fallback for Bootstrap 4
                $('#editTicketModal').modal('hide');
            }
        });
    });
});

// Add this function to get selected supporting team members
function getSelectedSupportingTeam() {
    const selectedMembers = [];
    const checkboxes = document.querySelectorAll('#supportingTeamOptions input[type="checkbox"]:checked');
    checkboxes.forEach(checkbox => {
        selectedMembers.push(checkbox.dataset.username);
    });
    return selectedMembers;
}

let currentTicketId = null;

async function showSupportingTeam(ticketId) {
    currentTicketId = ticketId;
    try {
        const [ticketResponse, usersResponse] = await Promise.all([
            fetchWithAuth(`/api/tickets/${ticketId}`),
            fetchWithAuth('/api/users/all')
        ]);

        const ticket = await ticketResponse.json();
        const users = await usersResponse.json();

        const optionsContainer = document.getElementById('supportingTeamModalOptions');
        optionsContainer.innerHTML = users.map(user => {
            const isSelected = Array.isArray(ticket.supportingTeam) && ticket.supportingTeam.includes(user.username);
            return `
                <div class="form-check">
                    <input type="checkbox" 
                           class="form-check-input" 
                           id="modal-user-${user._id}" 
                           value="${user._id}"
                           data-username="${user.username}"
                           ${isSelected ? 'checked' : ''}>
                    <label class="form-check-label" for="modal-user-${user._id}">
                        ${user.username}
                    </label>
                </div>
            `;
        }).join('');

        // Add search functionality
        const searchInput = document.getElementById('teamSearchInput');
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const teamMembers = optionsContainer.querySelectorAll('.form-check');
            teamMembers.forEach(member => {
                const username = member.querySelector('label').textContent.toLowerCase();
                if (username.includes(searchTerm)) {
                    member.style.display = 'block';
                } else {
                    member.style.display = 'none';
                }
            });
        });

        // Update the selected members display
        const selectedCheckboxes = optionsContainer.querySelectorAll('input[type="checkbox"]:checked');
        updateSelectedMembersModal(Array.from(selectedCheckboxes));
        
        const supportingTeamModal = new bootstrap.Modal(document.getElementById('supportingTeamModal'));
        supportingTeamModal.show();
    } catch (error) {
        console.error('Error loading supporting team:', error);
        console.error('Error details:', error.stack);
        alert('Error loading supporting team members');
    }
}

function updateSelectedMembersModal() {
    const selectedMembersBox = document.getElementById('selectedMembersBoxModal');
    const checkboxes = document.querySelectorAll('#supportingTeamModalOptions input[type="checkbox"]:checked');
    
    selectedMembersBox.innerHTML = Array.from(checkboxes).map(cb => `
        <span class="selected-member-tag" data-id="${cb.value}">
            ${cb.dataset.username}
            <i class="fas fa-times remove-member" onclick="removeTeamMemberModal('${cb.value}')"></i>
        </span>
    `).join('');
}

function removeTeamMemberModal(userId) {
    const checkbox = document.getElementById(`modal-user-${userId}`);
    if (checkbox) {
        checkbox.checked = false;
        updateSelectedMembersModal();
    }
}

async function saveSupportingTeam() {
    try {
        const selectedMembers = Array.from(
            document.querySelectorAll('#supportingTeamModalOptions input[type="checkbox"]:checked')
        ).map(cb => cb.dataset.username); // Make sure we're getting usernames

        console.log('Saving supporting team members:', selectedMembers);

        const response = await fetchWithAuth(`/api/tickets/${currentTicketId}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                supportingTeam: selectedMembers
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Failed to update supporting team');
        }

        const updatedTicket = await response.json();
        console.log('Successfully updated ticket:', updatedTicket);

        const supportingTeamModal = bootstrap.Modal.getInstance(document.getElementById('supportingTeamModal'));
        supportingTeamModal.hide();
        
        // Show success message
        showToast('Supporting team updated successfully', 'success');
        
        // Refresh the tickets table
        await loadTickets();
    } catch (error) {
        console.error('Error saving supporting team:', error);
        showAlert('Error saving supporting team members: ' + error.message, 'danger');
    }
}

// Add event listeners for the supporting team modal
document.addEventListener('DOMContentLoaded', function() {
    const supportingTeamModalOptions = document.getElementById('supportingTeamModalOptions');
    if (supportingTeamModalOptions) {
        supportingTeamModalOptions.addEventListener('change', function(e) {
            if (e.target.type === 'checkbox') {
                updateSelectedMembersModal();
            }
        });
    }
}); 

document.addEventListener('DOMContentLoaded', () => {
    const userRole = localStorage.getItem('role');
    const newTicketButton = document.querySelector('.tickets-header .btn-primary');
    
    // Hide/Show New Ticket button based on role
    if (newTicketButton) {
        if (userRole === 'Admin' || userRole === 'Project Manager') {
            newTicketButton.style.display = 'block';
        } else {
            newTicketButton.style.display = 'none';
        }
    }

    // Your existing DOMContentLoaded code...
    const token = localStorage.getItem('token');
    if (!token) {
        alert('Please log in to access this page');
        window.location.href = '/login.html';
        return;
    }
    
    updateStatistics();
    loadTickets();
});

</script>
<script>
    // Add this function to update user info including avatar
    function updateUserInterface() {
        const userRole = localStorage.getItem('role');
        const userName = localStorage.getItem('username');
        const userAvatar = localStorage.getItem('userAvatar');
        
        // Update username and role
        const userNameElement = document.querySelector('.user-name');
        const userRoleElement = document.querySelector('.user-role');
        if (userNameElement) userNameElement.textContent = `Welcome, ${userName || 'User'}`;
        if (userRoleElement) userRoleElement.textContent = userRole || 'Role';
        
        // Update all user photos on the page
        const userPhotos = document.querySelectorAll('.user-photo');
        userPhotos.forEach(photo => {
            if (userAvatar) {
                photo.src = userAvatar;
            } else {
                photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'; // Default photo
            }
        });
    }
    
    // Call this function when the page loads
    document.addEventListener('DOMContentLoaded', updateUserInterface);
    
    // Optional: Refresh user interface periodically to catch any changes
    setInterval(updateUserInterface, 30000); // Update every 30 seconds

    // Check for ticket creation success notification
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const toastNotification = document.getElementById('toast-notification');

        if (urlParams.has('ticket_created')) {
            toastNotification.textContent = 'Ticket created successfully!';
            toastNotification.classList.add('show');
            setTimeout(() => toastNotification.classList.remove('show'), 5000);
            // Clean up URL
            window.history.replaceState({}, document.title, window.location.pathname);
        }
    });

    // Table sorting function
function sortTable(columnIndex) {
    const table = document.querySelector('.table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    // Get the current sort direction
    const th = table.querySelectorAll('th')[columnIndex];
    const currentIcon = th.querySelector('i');
    const isAscending = currentIcon.classList.contains('fa-sort') || 
                        currentIcon.classList.contains('fa-sort-down');
    
    // Update all icons to default sort icon
    table.querySelectorAll('th i').forEach(icon => {
        icon.className = 'fas fa-sort';
    });
    
    // Update the clicked column's icon
    currentIcon.className = isAscending ? 'fas fa-sort-up' : 'fas fa-sort-down';
    
    // Sort the rows
    rows.sort((a, b) => {
        let aValue, bValue;
        
        // Special handling for date columns
        if (columnIndex === 6 || columnIndex === 7) { // Created Date or Due Date
            // Parse MM/DD/YYYY format
            const aText = a.cells[columnIndex].textContent.trim();
            const bText = b.cells[columnIndex].textContent.trim();
            
            // Parse dates in MM/DD/YYYY format
            const aParts = aText.split('/');
            const bParts = bText.split('/');
            
            if (aParts.length === 3 && bParts.length === 3) {
                // Create date objects with [month, day, year]
                // Month is 0-indexed in JavaScript Date
                aValue = new Date(aParts[2], aParts[0] - 1, aParts[1]).getTime();
                bValue = new Date(bParts[2], bParts[0] - 1, bParts[1]).getTime();
            } else {
                // Fallback if format is unexpected
                aValue = new Date(aText).getTime() || 0;
                bValue = new Date(bText).getTime() || 0;
            }
        } 
        // Special handling for status column (maintain custom order)
        else if (columnIndex === 5) { // Status column
            const statusOrder = { 'open': 0, 'in-progress': 1, 'resolved': 2 };
            aValue = statusOrder[a.querySelector('.status-select').value.toLowerCase()] || 0;
            bValue = statusOrder[b.querySelector('.status-select').value.toLowerCase()] || 0;
        }
        // Default string comparison
        else {
            aValue = a.cells[columnIndex].textContent.trim().toLowerCase();
            bValue = b.cells[columnIndex].textContent.trim().toLowerCase();
        }
        
        // Compare based on direction
        if (isAscending) {
            return aValue > bValue ? 1 : -1;
        } else {
            return aValue < bValue ? 1 : -1;
        }
    });
    
    // Re-append rows in the new order
    rows.forEach(row => tbody.appendChild(row));
}

// Initialize Bootstrap tooltips
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips when page loads
    initializeTooltips();
    
    // Also initialize tooltips after loading tickets
    const originalLoadTickets = window.loadTickets;
    window.loadTickets = async function() {
        await originalLoadTickets.apply(this, arguments);
        initializeTooltips();
    };
});

// Function to initialize tooltips
function initializeTooltips() {
    // Remove any existing tooltips first
    const existingTooltips = document.querySelectorAll('.tooltip');
    existingTooltips.forEach(tooltip => tooltip.remove());
    
    // Initialize tooltips with Bootstrap 5 syntax
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    tooltipTriggerList.forEach(function(tooltipTriggerEl) {
        // Check if Bootstrap 5
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            new bootstrap.Tooltip(tooltipTriggerEl, {
                placement: 'top',
                trigger: 'hover',
                delay: { show: 200, hide: 100 },
                container: 'body' // Append to body to avoid positioning issues
            });
        } 
        // Fallback for Bootstrap 4
        else if (typeof $ !== 'undefined' && $.fn.tooltip) {
            $(tooltipTriggerEl).tooltip({
                placement: 'top',
                trigger: 'hover',
                delay: { show: 200, hide: 100 },
                container: 'body'
            });
        }
    });
    
    console.log('Tooltips initialized for', tooltipTriggerList.length, 'elements');
}

// Call this function after the DOM is loaded and after tickets are loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips when page loads
    setTimeout(initializeTooltips, 500); // Add a small delay to ensure DOM is ready
    
    // Re-initialize tooltips after table updates
    const ticketsTableBody = document.getElementById('ticketsTableBody');
    if (ticketsTableBody) {
        // Use MutationObserver to detect when table content changes
        const observer = new MutationObserver(function(mutations) {
            setTimeout(initializeTooltips, 200);
        });
        
        observer.observe(ticketsTableBody, { childList: true, subtree: true });
    }
}); 
    </script>

    <!-- Add this before </body> -->
    <script>
    // Function to handle comment updates
    async function updateTicketComment(textarea) {
        const ticketId = textarea.getAttribute('data-ticket-id');
        const comment = textarea.value;

        console.log('Updating comment:', {
            ticketId,
            comment
        });

        try {
            const response = await fetchWithAuth(`/api/tickets/${ticketId}/comments`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ comments: comment })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to update comment');
            }

            const updatedTicket = await response.json();
            console.log('Comment updated successfully:', updatedTicket);

            // Show success message
            showToast('Comment updated successfully', 'success');
        } catch (error) {
            console.error('Error updating comment:', error);
            showToast(`Failed to update comment: ${error.message}`, 'error');
        }
    }

    // Helper function to show toast messages
    function showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast show ${type}`;
        toast.innerHTML = `
            <div class="toast-body">
                ${message}
            </div>
        `;
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 3000);
    }

    // Function to handle authenticated fetch requests
    async function fetchWithAuth(url, options = {}) {
        const token = localStorage.getItem('token');
        if (!token) {
            throw new Error('No authentication token found');
        }

        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
            ...options.headers
        };

        return fetch(url, { ...options, headers });
    }

    // Add event listener for when the DOM is fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize any necessary event listeners here
        console.log('DOM fully loaded');
    });
    </script>


    <script>
    document.querySelectorAll('.card-flip').forEach(card => {
      card.addEventListener('click', function() {
        card.classList.toggle('flipped');
      });
    });
    </script>

    <script>
    function renderOverdueTickets(tickets) {
      const container = document.getElementById('overdue-tickets-list');
      container.innerHTML = '';
      tickets.forEach(ticket => {
        container.appendChild(createOverdueTicketCard(ticket));
      });
    }
    </script>

    <script>
    function createOverdueTicketCard(ticket) {
      const cardFlip = document.createElement('div');
      cardFlip.className = 'card-flip';

      cardFlip.innerHTML = `
        <div class="card-inner">
          <div class="card-front overdue-ticket-item">
            <div class="ticket-header">
              <span class="project-id">${ticket.projectId || ''}</span>
              <span class="ticket-status ${ticket.status.toLowerCase()}">${ticket.status}</span>
            </div>
            <div class="ticket-project">${ticket.projectName || 'No Project Name'}</div>
            <div class="ticket-title">${ticket.ticketHeading || 'No Heading'}</div>
            <div class="ticket-meta mt-2">
              <div><i class="fas fa-user"></i> ${ticket.assignedTo || 'Unassigned'}</div>
              <div><i class="fas fa-calendar-alt"></i> Due: ${formatDateCST(ticket.dueDate)}</div>
            </div>
          </div>
          <div class="card-back overdue-ticket-item">
            <div><strong>Project ID:</strong> <span class="project-id">${ticket.projectId || ''}</span></div>
            <div><strong>Ticket Number:</strong> <span class="ticket-number">${ticket.ticketNumber || ''}</span></div>
            <div><strong>Comment:</strong> <span class="ticket-comment">${ticket.comments || ''}</span></div>
          </div>
        </div>
      `;

      // Add flip event
      cardFlip.addEventListener('click', function(e) {
        // Prevent flip when clicking a button (if you add any)
        if (e.target.tagName.toLowerCase() === 'button') return;
        cardFlip.classList.toggle('flipped');
      });

      return cardFlip;
    }
    </script>

    <script>
    // Format date in CST/CDT (America/Chicago) as MM/DD/YYYY
    function formatDateCST(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '';
        const parts = new Intl.DateTimeFormat('en-US', {
            timeZone: 'America/Chicago',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        }).formatToParts(date);
        const month = parts.find(p => p.type === 'month').value;
        const day = parts.find(p => p.type === 'day').value;
        const year = parts.find(p => p.type === 'year').value;
        return `${month}/${day}/${year}`;
    }
    // For local time display (with time)
    function formatDateLocal(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '';
        return date.toLocaleString(); // Or customize as needed
    }
    </script>

    <script>
    // Replace old helper with Luxon-based version
    function getChicagoISOStringFromDateInput(dateString) {
        // dateString is 'YYYY-MM-DD'
        return luxon.DateTime.fromISO(dateString, { zone: 'America/Chicago' })
            .startOf('day')
            .toUTC()
            .toISO();
    }
    // ... existing code ...
    document.getElementById('newTicketForm').addEventListener('submit', async function(event) {
        // ... existing code ...
        const newTicket = {
            // ... existing fields ...
            dueDate: getChicagoISOStringFromDateInput(document.getElementById('dueDate').value),
            // ... existing fields ...
        };
        // ... existing code ...
    });
    // ... existing code ...
    async function saveTicketChanges() {
        // ... existing code ...
        const ticketData = {
            // ... existing fields ...
            dueDate: getChicagoISOStringFromDateInput(document.getElementById('editDueDate').value),
            // ... existing fields ...
        };
        // ... existing code ...
    }
    // ... existing code ...
    </script>
</body>
</html>


