<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Project</title>
    <link rel="stylesheet" href="styles/style.css">
    <!-- Bootstrap CSS -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Add this to the head section of your HTML -->
    <link rel="stylesheet" href="styles/new-project.css">
    <!-- Add Quill CSS and JS -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <style>
        #edit-project-form button[type="button"].btn-primary {
            margin-top: 24px !important; /* Creates clear separation */
        }
        #edit-project-form button[type="button"].btn-secondary {
            margin-top: 24px !important; /* Creates clear separation */
        }
        /* Override Quill container height to prevent layout issues */
        .ql-container {
            height: auto !important;
        }
        
        /* Supporting Team Custom Dropdown Styles */
        .supporting-team-dropdown {
            position: relative; /* This is crucial for z-index to work */
            z-index: 1050; /* A high z-index, common for modals/dropdowns */
        }
        
        .supporting-team-menu {
            /* Bootstrap handles positioning, we just need the parent to have a z-index */
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="sidebar">
                <h2>Dashboards</h2>
                <hr class="divider">
                <nav>
                    <h3>PROJECT LIST</h3>
                    
                    <!-- Search bar under PROJECT LIST heading -->
                    <div class="sidebar-search">
                      <div class="input-group">
                        <input type="text" id="project-search" class="form-control form-control-sm" placeholder="Search by Project ID...">
                        <div class="input-group-append">
                          <button class="btn btn-sm btn-outline-secondary" type="button" id="clear-search">
                            <i class="fas fa-times"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Category selector under PROJECT LIST heading -->
                    <div class="sidebar-category mt-3 mb-2">
                      <select id="project-category" class="form-control form-control-sm">
                        <option value="all">All Projects</option>
                        <option value="client">By Client</option>
                      </select>
                    </div>
                    
                    <!-- Project list container -->
                    <div id="project-list-container">
                      <ul id="project-list">
                        <!-- Projects will be loaded here -->
                      </ul>
                    </div>
                    
                    <!-- Client category view (initially hidden) -->
                    <div id="client-category-container" style="display: none;">
                      <div id="client-accordion" class="accordion">
                        <!-- Client categories will be dynamically loaded here -->
                      </div>
                    </div>
                </nav>
            </div>
            <div class="main-wrapper">
                <header class="top-bar">
                    <div class="left-section">
                        <button onclick="goHome()" class="home-button">
                            <i class="fas fa-home"></i> <span class="home-text">Home</span>
                        </button>
                        <div class="dropdown-container">
                            <div class="dropdown">
                                <button class="dropdown-button">Production</button>
                                <div class="dropdown-content">
                                    <a href="Move-to-Production.html">Move to Production</a>
                                </div>
                            </div>
                            <div class="dropdown">
                                <button class="dropdown-button">Reports</button>
                                <div class="dropdown-content">
                                    <a href="WIP-report.html">WIP Report</a>
                                </div>
                            </div>
                            <div class="dropdown">
                                <button class="dropdown-button">Project Management</button>
                                <div class="dropdown-content">
                                    <a href="Invoices.html">Invoice</a>
                                </div>
                            </div>
                            <div class="dropdown">
                                <button class="dropdown-button">Tickets</button>
                                <div class="dropdown-content">
                                    <a href="Tickets.html">Tickets</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="user-info">
                        <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp" alt="User Photo" class="user-photo" onerror="this.onerror=null;this.src='https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';" />
                        <div class="user-details">
                            <span class="user-name">Welcome, User</span>
                            <span class="user-role">Role</span>
                        </div>
                        <button onclick="logout()" class="logout-button">Logout</button>
                    </div>
                </header>
                
                <div class="main-content" id="main-content">
                    <div class="project-create-container" style="padding: 20px; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);">
                        <h2 class="text-center mb-4">Edit Project</h2>
                        <form id="edit-project-form" class="form-container">
                            <div class="project-details-content">
                                <!-- Project ID -->
                                <div class="project-details-box">
                                    <label for="edit-projectId">Project ID</label>
                                    <input type="text" id="edit-projectId" class="form-control" readonly />
                                    <small class="form-text text-muted">A unique identifier for this project.</small>
                                </div>
                                <!-- Project Title -->
                                <div class="project-details-box">
                                    <label for="edit-title">Project Title</label>
                                    <input type="text" id="edit-title" class="form-control" />
                                    <small class="form-text text-muted">The full, official title of the project.</small>
                                </div>
                                <!-- Client Name -->
                                <div class="project-details-box">
                                    <label for="clientNameDropdown">Client Name</label>
                                    <select class="form-control" id="clientNameDropdown" name="clientName" required>
                                        <option value="">Select Client</option>
                                    </select>
                                    <small class="form-text text-muted">Choose the client from the list.</small>
                                </div>
                                <!-- Starting Date -->
                                <div class="project-details-box">
                                    <label for="edit-startDate">Starting Date</label>
                                    <input type="date" id="edit-startDate" class="form-control" />
                                    <small class="form-text text-muted">The official start date of the project.</small>
                                </div>
                                <!-- End Date -->
                                <div class="project-details-box">
                                    <label for="edit-endDate">End Date</label>
                                    <input type="date" id="edit-endDate" class="form-control" />
                                    <small class="form-text text-muted">The expected completion or due date.</small>
                                </div>
                                <!-- Project Manager -->
                                <div class="project-details-box">
                                    <label for="projectManagerDropdown">Project Manager Name</label>
                                    <select class="form-control" id="projectManagerDropdown" name="projectManager">
                                        <option value="">Select Project Manager</option>
                                    </select>
                                    <small class="form-text text-muted">The primary manager for this project.</small>
                                </div>
                                <!-- Creative Service Lead -->
                                <div class="project-details-box">
                                    <label for="creativeServiceDropdown">Creative Service Lead Name</label>
                                    <select class="form-control" id="creativeServiceDropdown" name="creativeServiceLead">
                                        <option value="">Select Creative Service Lead</option>
                                    </select>
                                    <small class="form-text text-muted">Lead for creative services.</small>
                                </div>
                                <!-- Creative Acquisition Lead -->
                                <div class="project-details-box">
                                    <label for="creativeAcquisitionDropdown">Creative Acquisition Lead Name</label>
                                    <select class="form-control" id="creativeAcquisitionDropdown" name="creativeAcquisitionLead">
                                        <option value="">Select Creative Acquisition Lead</option>
                                    </select>
                                    <small class="form-text text-muted">Lead for creative acquisition.</small>
                                </div>
                                <!-- Editorial Lead -->
                                <div class="project-details-box">
                                    <label for="editorialDropdown">Editorial Lead</label>
                                    <select class="form-control" id="editorialDropdown" name="editorialLead">
                                        <option value="">Select Editorial Lead</option>
                                    </select>
                                    <small class="form-text text-muted">The main contact for editorial.</small>
                                </div>
                                <!-- QC Lead -->
                                <div class="project-details-box">
                                    <label for="qcDropdown">QC Lead</label>
                                    <select class="form-control" id="qcDropdown" name="qcLead">
                                        <option value="">Select QC Lead</option>
                                    </select>
                                    <small class="form-text text-muted">The lead for quality control.</small>
                                </div>
                                <!-- Page Count -->
                                <div class="project-details-box">
                                    <label for="edit-pageCount">Page Count</label>
                                    <input type="number" id="edit-pageCount" class="form-control" />
                                    <small class="form-text text-muted">Total number of pages in the project.</small>
                                </div>
                                <!-- ISBN -->
                                <div class="project-details-box">
                                    <label for="edit-isbn-container">ISBN</label>
                                    <div id="edit-isbn-container">
                                        <!-- ISBN fields will be added here -->
                                    </div>
                                    <button type="button" class="btn btn-sm btn-success mt-2" onclick="addIsbnField()">
                                        <i class="fas fa-plus"></i> Add ISBN
                                    </button>
                                    <small class="form-text text-muted">Add one or more ISBNs.</small>
                                </div>
                                <!-- Trim Size -->
                                <div class="project-details-box">
                                    <label for="edit-trimSize">Trim Size</label>
                                    <input type="text" id="edit-trimSize" class="form-control" />
                                    <small class="form-text text-muted">e.g., 8.5 x 11 in</small>
                                </div>
                                <!-- Color -->
                                <div class="project-details-box">
                                    <label for="edit-color">Color</label>
                                    <select id="edit-color" name="color" class="form-control">
                                        <option value="1/C">1/C</option>
                                        <option value="4/C">4/C</option>
                                    </select>
                                    <small class="form-text text-muted">Select the color profile.</small>
                                </div>
                                <!-- Bleed -->
                                <div class="project-details-box">
                                    <label for="edit-bleed">Bleed</label>
                                    <input type="text" id="edit-bleed" class="form-control" />
                                    <small class="form-text text-muted">Specify bleed requirements (e.g., 0.125 in).</small>
                                </div>
                                <!-- Combined Digital Project Box -->
                                <div class="project-details-box">
                                    <div class="form-group mb-2">
                                        <label for="edit-isDigitalProject">Is this a digital project?</label>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="edit-isDigitalProject" name="isDigitalProject">
                                            <label class="form-check-label" for="edit-isDigitalProject">Yes</label>
                                        </div>
                                        <small class="form-text text-muted">Check if this is primarily a digital product.</small>
                                    </div>
                                    <div class="form-group mb-0" id="edit-digitalLead-row" style="display:none;">
                                        <label for="digitalLeadDropdown">Digital Lead</label>
                                        <select class="form-control" id="digitalLeadDropdown" name="digitalLead">
                                            <option value="">Select Digital Lead</option>
                                        </select>
                                        <small class="form-text text-muted">Assign the lead for digital components.</small>
                                    </div>
                                </div>
                                <!-- Levels -->
                                <div class="project-details-box">
                                    <label for="edit-levels">Levels</label>
                                    <input type="number" id="edit-levels" class="form-control" readonly />
                                    <small class="form-text text-muted">Number of levels or books in the project</small>
                                </div>
                                <!-- Units -->
                                <div class="project-details-box">
                                    <label for="edit-units">Units</label>
                                    <input type="number" id="edit-units" class="form-control" readonly />
                                    <small class="form-text text-muted">Number of units or chapters per level or book.</small>
                                </div>
                                <!-- Parent Project ID -->
                                <div class="project-details-box">
                                    <label for="edit-parentProjectId">Parent Project ID</label>
                                    <input type="text" class="form-control" id="edit-parentProjectId" name="parentProjectId">
                                    <small class="form-text text-muted">Enter an existing project ID or leave as is to use current project ID</small>
                                </div>
                                <!-- New wrapper for side-by-side layout -->
                                <div class="d-flex w-100" style="gap: 10px;">
                                    <!-- Supporting Team -->
                                    <div class="project-details-box" style="flex: 1;">
                                        <label for="supportingTeam">Supporting Team</label>
                                        <div class="supporting-team-dropdown">
                                            <button class="supporting-team-btn form-control d-flex justify-content-between align-items-center" 
                                                    type="button" 
                                                    id="editSupportingTeamDropdown">
                                                <span class="selected-text">Select Supporting Team Members</span>
                                                <i class="fas fa-chevron-down"></i>
                                            </button>
                                            <div class="supporting-team-menu">
                                                <div class="search-container mb-2">
                                                    <div class="input-group">
                                                        <span class="input-group-text">
                                                            <i class="fas fa-search"></i>
                                                        </span>
                                                        <input type="text" 
                                                               class="form-control" 
                                                               id="editSupportingTeamSearch" 
                                                               placeholder="Search team members..."
                                                               onclick="event.stopPropagation()">
                                                    </div>
                                                </div>
                                                <div id="editSupportingTeamOptions" 
                                                     class="team-options-container"
                                                     onclick="event.stopPropagation()">
                                                    <!-- Team members will be loaded here -->
                                                </div>
                                            </div>
                                        </div>
                                        <div id="edit-selectedMembersBox" class="selected-members-box mt-2">
                                            <!-- Selected members will be displayed here -->
                                        </div>
                                        <input type="hidden" id="edit-supportingTeam" name="supportingTeam">
                                        <small class="form-text text-muted">Select team members who will support this project.</small>
                                    </div>
                                    <!-- Notes -->
                                    <div class="project-details-box" style="flex: 1;">
                                        <label for="notes-editor">Notes</label>
                                        <div id="notes-editor" class="quill-editor"></div>
                                        <input type="hidden" id="edit-notes" name="notes">
                                        <small class="form-text text-muted">Add any important project notes or details.</small>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group text-center mt-4">
                                <button type="button" class="btn btn-primary" onclick="updateProject()">Save Changes</button>
                                <button type="button" class="btn btn-secondary ml-2" onclick="cancelEdit()">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    
    <script>
        let currentProjectId = null;
        let allProjects = [];
        let notesQuill;
        let loadedProjectData = null;
        
        document.addEventListener("DOMContentLoaded", function() {
            // Initialize Quill editor
            notesQuill = new Quill('#notes-editor', {
                theme: 'snow',
                modules: {
                    toolbar: [
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                        ['link', 'image'],
                        ['clean']
                    ]
                },
                placeholder: 'Add any additional notes about the project...'
            });
            
            // Update hidden input when editor content changes
            notesQuill.on('text-change', function() {
                document.getElementById('edit-notes').value = notesQuill.root.innerHTML;
            });
            
            // Get project ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const projectId = urlParams.get('projectId');
            
            if (projectId) {
                currentProjectId = projectId;
                loadProjectDetails(projectId);
            }
            
            // Load projects for sidebar
            loadProjects();
            
            // Add event listeners for search and category selection
            const searchInput = document.getElementById('project-search');
            const clearSearchBtn = document.getElementById('clear-search');
            const categorySelect = document.getElementById('project-category');
            
            searchInput.addEventListener('input', filterProjects);
            clearSearchBtn.addEventListener('click', clearSearch);
            categorySelect.addEventListener('change', toggleCategoryView);
            
            // Load team members
            loadTeamMembers();

            // Fetch clients and populate the dropdown
            fetchClients();
        });
        
        async function loadProjectDetails(projectId) {
            try {
                const token = localStorage.getItem("token");
                if (!token) {
                    alert("No authentication token found. Please log in again.");
                    window.location.href = "/login.html";
                    return;
                }
                
                // Get the source parameter from URL
                const urlParams = new URLSearchParams(window.location.search);
                const source = urlParams.get('source') || 'inprogress';
                
                // Determine the API endpoint based on the source
                let apiEndpoint;
                if (source === 'yettostart') {
                    apiEndpoint = `/api/yettostartprojects/${projectId}`;
                    // Update page title to reflect we're editing a yet-to-start project
                    document.querySelector('h2.mb-4').textContent = 'Edit Yet to Start Project';
                } else {
                    apiEndpoint = `/api/inprogressprojects/${projectId}`;
                }
                
                const response = await fetch(apiEndpoint, {
                    headers: {
                        Authorization: `Bearer ${token}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`Failed to fetch project details: ${response.status} ${response.statusText}`);
                }
                
                const project = await response.json();
                loadedProjectData = project; // Store globally
                
                // Populate client dropdown with all options first
                await fetchClients();
                document.getElementById("clientNameDropdown").value = project.clientName || '';
                
                // Populate form fields
                document.getElementById("edit-projectId").value = project._id;
                document.getElementById("edit-title").value = project.title || '';
                document.getElementById("edit-startDate").value = project.startDate ? new Date(project.startDate).toISOString().split('T')[0] : '';
                document.getElementById("edit-endDate").value = project.endDate ? new Date(project.endDate).toISOString().split('T')[0] : '';
                document.getElementById("projectManagerDropdown").value = project.projectManager || '';
                document.getElementById("creativeServiceDropdown").value = project.creativeServiceLead || '';
                document.getElementById("creativeAcquisitionDropdown").value = project.creativeAcquisitionLead || '';
                document.getElementById("edit-pageCount").value = project.pageCount || '';
                document.getElementById("edit-trimSize").value = project.trimSize || '';
                document.getElementById("edit-color").value = project.color || '';
                document.getElementById("edit-levels").value = project.levels || '';
                document.getElementById("edit-units").value = project.units || '';
                document.getElementById("edit-parentProjectId").value = project.parentProjectId || project._id;
                document.getElementById("editorialDropdown").value = project.editorialLead || '';
                document.getElementById("qcDropdown").value = project.qcLead || '';
                document.getElementById("edit-bleed").value = project.bleed || '';
                document.getElementById("edit-isDigitalProject").checked = !!project.isDigitalProject;
                const digitalLeadRow = document.getElementById("edit-digitalLead-row");
                digitalLeadRow.style.display = project.isDigitalProject ? '' : 'none';
                document.getElementById("digitalLeadDropdown").value = project.digitalLead || '';
                document.getElementById("edit-isDigitalProject").addEventListener('change', function() {
                    digitalLeadRow.style.display = this.checked ? '' : 'none';
                });
                
                // Store the source for use when saving
                document.getElementById("edit-project-form").setAttribute("data-source", source);
                
                // Set notes in Quill editor
                if (notesQuill) {
                    notesQuill.root.innerHTML = project.notes || '';
                    document.getElementById("edit-notes").value = project.notes || '';
                }
                
                // Handle ISBNs
                const isbnContainer = document.getElementById("edit-isbn-container");
                isbnContainer.innerHTML = '';
                
                if (project.isbns && project.isbns.length > 0) {
                    // Add each ISBN from the array
                    project.isbns.forEach((isbn) => {
                        addIsbnField(isbn);
                    });
                } else if (project.isbn) {
                    // Fallback to single ISBN
                    addIsbnField(project.isbn);
                } else {
                    // No ISBN, add empty field
                    addIsbnField('');
                }
                
                // Set supporting team
                if (project.supportingTeam && project.supportingTeam.length > 0) {
                    // First store the value in the hidden input
                    document.getElementById("edit-supportingTeam").value = JSON.stringify(project.supportingTeam);
                    
                    // Then update the UI with the selected team members
                    // Wait a bit to ensure team members are loaded first
                    setTimeout(() => {
                        updateSelectedTeamMembers(project.supportingTeam);
                    }, 300);
                }
                // --- Call populateRoleDropdowns with selected values ---
                populateRoleDropdowns({
                    projectManager: project.projectManager,
                    creativeServiceLead: project.creativeServiceLead,
                    creativeAcquisitionLead: project.creativeAcquisitionLead,
                    editorialLead: project.editorialLead,
                    qcLead: project.qcLead,
                    digitalLead: project.digitalLead
                });
            } catch (error) {
                console.error("Error loading project details:", error);
                alert("Error loading project details. Please try again.");
            }
        }
        
        function addIsbnField(value = "") {
            const isbnContainer = document.getElementById("edit-isbn-container");
            const isbnEntry = document.createElement("div");
            isbnEntry.className = "isbn-entry input-group mb-2";
            isbnEntry.innerHTML = `
                <input type="text" class="form-control edit-isbn-input" value="${value}" placeholder="Enter ISBN">
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger remove-isbn-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            isbnEntry.querySelector(".remove-isbn-btn").addEventListener("click", function() {
                isbnContainer.removeChild(isbnEntry);
            });
            
            isbnContainer.appendChild(isbnEntry);
        }
        
        async function loadTeamMembers() {
            try {
                const token = localStorage.getItem("token");
                const response = await fetch('/api/users/all', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error('Failed to fetch team members');
                }
                
                const users = await response.json();
                const teamOptionsContainer = document.getElementById("editSupportingTeamOptions");
                teamOptionsContainer.innerHTML = "";
                
                // Create checkboxes for each team member
                users.forEach(user => {
                    const option = document.createElement("div");
                    option.className = "form-check";
                    option.innerHTML = `
                        <input class="form-check-input" type="checkbox" value="${user.username}" 
                               id="team-${user.username}" data-username="${user.username}">
                        <label class="form-check-label d-flex justify-content-between" for="team-${user.username}">
                            <span>${user.username}</span>
                            <small class="text-muted">${user.role || ''}</small>
                        </label>
                    `;
                    teamOptionsContainer.appendChild(option);
                });
                
                // Add event listeners to checkboxes
                const checkboxes = teamOptionsContainer.querySelectorAll("input[type='checkbox']");
                checkboxes.forEach(checkbox => {
                    checkbox.addEventListener("change", updateSelectedTeamMembers);
                });
                
                // Set up search functionality
                document.getElementById('editSupportingTeamSearch').addEventListener('input', function(e) {
                    const searchTerm = e.target.value.toLowerCase();
                    document.querySelectorAll('#editSupportingTeamOptions .form-check').forEach(item => {
                        const username = item.querySelector('label span').textContent.toLowerCase();
                        const role = item.querySelector('label small').textContent.toLowerCase();
                        if (username.includes(searchTerm) || role.includes(searchTerm)) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
                
                // Set up dropdown toggle
                const dropdownBtn = document.getElementById('editSupportingTeamDropdown');
                dropdownBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const menu = document.querySelector('.supporting-team-menu');
                    menu.classList.toggle('show');
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!e.target.closest('.supporting-team-dropdown')) {
                        document.querySelector('.supporting-team-menu').classList.remove('show');
                    }
                });
            } catch (error) {
                console.error("Error loading team members:", error);
                document.getElementById("editSupportingTeamOptions").innerHTML = 
                    "<div class='alert alert-danger'>Error loading team members</div>";
            }
        }
        
        function updateSelectedTeamMembers(preselectedTeam = null) {
            const selectedMembers = [];
            const checkboxes = document.querySelectorAll("#editSupportingTeamOptions input[type='checkbox']");
            
            // If preselected team is provided, check those checkboxes
            if (preselectedTeam && Array.isArray(preselectedTeam)) {
                checkboxes.forEach(checkbox => {
                    const username = checkbox.dataset.username;
                    if (preselectedTeam.includes(username)) {
                        checkbox.checked = true;
                        selectedMembers.push(username);
                    } else {
                        checkbox.checked = false;
                    }
                });
            } else {
                // Otherwise, get selected checkboxes
                checkboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        selectedMembers.push(checkbox.dataset.username);
                    }
                });
            }
            
            // Update hidden input
            document.getElementById("edit-supportingTeam").value = JSON.stringify(selectedMembers);
            
            // Update selected members display
            const selectedTeamContainer = document.getElementById('edit-selectedMembersBox');
            selectedTeamContainer.innerHTML = "";
            
            if (selectedMembers.length > 0) {
                selectedMembers.forEach(member => {
                    const badge = document.createElement("span");
                    badge.className = "selected-member-tag";
                    badge.innerHTML = `${member} <i class="fas fa-times remove-member" onclick="removeTeamMember('${member}')"></i>`;
                    selectedTeamContainer.appendChild(badge);
                });
            } else {
                selectedTeamContainer.innerHTML = "<p class='text-muted'>No team members selected</p>";
            }
            
            // Update dropdown button text
            const selectedText = document.querySelector('.supporting-team-btn .selected-text');
            if (selectedMembers.length === 0) {
                selectedText.textContent = 'Select Supporting Team Members';
            } else if (selectedMembers.length <= 2) {
                selectedText.textContent = selectedMembers.join(', ');
            } else {
                selectedText.textContent = `${selectedMembers.length} members selected`;
            }
        }
        
        function removeTeamMember(username) {
            const checkbox = document.querySelector(`#editSupportingTeamOptions input[data-username="${username}"]`);
            if (checkbox) {
                checkbox.checked = false;
                updateSelectedTeamMembers();
            }
        }
        
        async function updateProject() {
            try {
                // Collect all ISBN values
                const isbnInputs = document.querySelectorAll(".edit-isbn-input");
                const isbnValues = Array.from(isbnInputs)
                    .map(input => input.value.trim())
                    .filter(value => value !== "");
                
                // Get supporting team from hidden input
                let supportingTeam = [];
                try {
                    supportingTeam = JSON.parse(document.getElementById("edit-supportingTeam").value || "[]");
                } catch (e) {
                    console.error("Error parsing supporting team:", e);
                }
                
                const updatedProject = {
                    title: document.getElementById("edit-title").value,
                    clientName: document.getElementById("clientNameDropdown").value,
                    startDate: document.getElementById("edit-startDate").value,
                    endDate: document.getElementById("edit-endDate").value,
                    projectManager: document.getElementById("projectManagerDropdown").value,
                    creativeServiceLead: document.getElementById("creativeServiceDropdown").value,
                    creativeAcquisitionLead: document.getElementById("creativeAcquisitionDropdown").value,
                    pageCount: document.getElementById("edit-pageCount").value,
                    isbns: isbnValues,
                    isbn: isbnValues.length > 0 ? isbnValues[0] : "",
                    trimSize: document.getElementById("edit-trimSize").value,
                    color: document.getElementById("edit-color").value,
                    levels: document.getElementById("edit-levels").value,
                    units: document.getElementById("edit-units").value,
                    parentProjectId: document.getElementById("edit-parentProjectId").value,
                    supportingTeam: supportingTeam,
                    notes: document.getElementById("edit-notes").value,
                    editorialLead: document.getElementById("editorialDropdown").value,
                    qcLead: document.getElementById("qcDropdown").value,
                    bleed: document.getElementById("edit-bleed").value,
                    isDigitalProject: document.getElementById("edit-isDigitalProject").checked,
                    digitalLead: document.getElementById("digitalLeadDropdown").value
                };
                
                const token = localStorage.getItem("token");
                const projectId = document.getElementById("edit-projectId").value;
                
                // Determine the API endpoint based on the source
                const source = document.getElementById("edit-project-form").getAttribute("data-source") || 'inprogress';
                let apiEndpoint;
                let redirectUrl;
                
                if (source === 'yettostart') {
                    apiEndpoint = `/api/yettostartprojects/${projectId}`;
                    redirectUrl = "Move-to-Production.html";
                } else {
                    apiEndpoint = `/api/inprogressproject/${projectId}`;
                    redirectUrl = "index.html";
                }
                
                const response = await fetch(apiEndpoint, {
                    method: "PUT",
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${token}`
                    },
                    body: JSON.stringify(updatedProject)
                });
                
                if (!response.ok) {
                    throw new Error(`Failed to update project: ${response.status} ${response.statusText}`);
                }
                
                alert("Project updated successfully");
                window.location.href = redirectUrl;
            } catch (error) {
                console.error("Error updating project:", error);
                alert("Error updating project. Please try again.");
            }
        }
        
        function cancelEdit() {
            // Determine where to redirect based on the source
            const source = document.getElementById("edit-project-form").getAttribute("data-source") || 'inprogress';
            
            if (source === 'yettostart') {
                window.location.href = "Move-to-Production.html";
            } else {
                window.location.href = "index.html";
            }
        }
        
        async function loadProjects() {
            try {
                const token = localStorage.getItem("token");
                const userRole = localStorage.getItem("role");
                const username = localStorage.getItem("username");
                
                // Determine which projects to load based on the source
                const urlParams = new URLSearchParams(window.location.search);
                const source = urlParams.get('source') || 'inprogress';
                
                let apiEndpoint;
                if (source === 'yettostart') {
                    apiEndpoint = "/api/yettostartprojects";
                } else {
                    apiEndpoint = "/api/inprogressprojects";
                }
                
                const response = await fetch(apiEndpoint, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                });
                
                if (!response.ok) {
                    throw new Error("Failed to fetch projects");
                }
                
                const projects = await response.json();
                
                // Filter projects based on user role
                let filteredProjects = projects;
                
                if (userRole !== "Admin") {
                    // Filter projects where the user is involved
                    filteredProjects = projects.filter(project => {
                        return (
                            project.projectManager === username || 
                            project.creativeServiceLead === username || 
                            project.creativeAcquisitionLead === username ||
                            (Array.isArray(project.supportingTeam) && project.supportingTeam.includes(username))
                        );
                    });
                }
                
                // Store all filtered projects for search functionality
                allProjects = filteredProjects;
                
                // Display projects
                displayProjectsList(filteredProjects);
                prepareClientCategoryView(filteredProjects);
                
            } catch (error) {
                console.error("Error loading projects:", error);
                document.getElementById("project-list").innerHTML = "<li>Error loading projects</li>";
            }
        }
        
        function displayProjectsList(projects) {
            const projectList = document.getElementById("project-list");
            projectList.innerHTML = "";
            
            projects.forEach((project) => {
                const li = document.createElement("li");
                const a = document.createElement("a");
                a.href = `Project-details.html?projectId=${project._id}`;
                a.textContent = `${project._id} - ${project.title}`;
                a.title = `${project._id} - ${project.title}`;
                li.appendChild(a);
                projectList.appendChild(li);
            });
        }
        
        function prepareClientCategoryView(projects) {
            const clientAccordion = document.getElementById("client-accordion");
            clientAccordion.innerHTML = "";
            
            // Group projects by client
            const clientGroups = {};
            projects.forEach(project => {
                const client = project.clientName || "Uncategorized";
                if (!clientGroups[client]) {
                    clientGroups[client] = [];
                }
                clientGroups[client].push(project);
            });
            
            // Sort clients alphabetically
            const sortedClients = Object.keys(clientGroups).sort();
            
            // Create client list
            const clientList = document.createElement("ul");
            clientList.id = "client-list";
            
            sortedClients.forEach(client => {
                const clientProjects = clientGroups[client];
                const projectCount = clientProjects.length;
                
                // Create client list item
                const li = document.createElement('li');
                const a = document.createElement('a');
                a.href = "#";
                a.innerHTML = `${client} <span class="project-count">${projectCount}</span>`;
                a.setAttribute("data-client", client);
                a.setAttribute("onclick", `toggleClientProjects('${client}')`);
                li.appendChild(a);
                
                // Create hidden projects list for this client
                const projectsList = document.createElement('ul');
                projectsList.className = 'client-projects';
                projectsList.id = `client-projects-${client.replace(/\s+/g, '').replace(/[^a-zA-Z0-9]/g, '')}`;
                projectsList.style.display = 'none';
                
                clientProjects.forEach(project => {
                    const projectLi = document.createElement('li');
                    projectLi.className = 'client-project-item';
                    const projectA = document.createElement('a');
                    projectA.href = `Project-details.html?projectId=${project._id}`;
                    projectA.textContent = `${project._id} - ${project.title}`;
                    projectA.title = `${project._id} - ${project.title}`;
                    projectLi.appendChild(projectA);
                    projectsList.appendChild(projectLi);
                });
                
                li.appendChild(projectsList);
                clientList.appendChild(li);
            });
            
            clientAccordion.appendChild(clientList);
        }
        
        function toggleClientProjects(client) {
            const clientId = client.replace(/\s+/g, '').replace(/[^a-zA-Z0-9]/g, '');
            const projectsList = document.getElementById(`client-projects-${clientId}`);
            
            if (projectsList.style.display === 'none') {
                // Hide all other client projects first
                document.querySelectorAll('.client-projects').forEach(list => {
                    list.style.display = 'none';
                });
                
                // Show this client's projects
                projectsList.style.display = 'block';
                
                // Highlight the selected client
                document.querySelectorAll('#client-list > li > a').forEach(a => {
                    a.classList.remove('active');
                });
                document.querySelector(`#client-list a[data-client="${client}"]`).classList.add('active');
            } else {
                // Toggle off if already showing
                projectsList.style.display = 'none';
                document.querySelector(`#client-list a[data-client="${client}"]`).classList.remove('active');
            }
        }
        
        function filterProjects() {
            const searchTerm = document.getElementById('project-search').value.toLowerCase();
            const categoryView = document.getElementById('project-category').value;
            
            if (searchTerm === '') {
                // If search is empty, show all projects
                if (categoryView === 'all') {
                    displayProjectsList(allProjects);
                } else {
                    // Just refresh the client view without filtering
                    prepareClientCategoryView(allProjects);
                }
                return;
            }
            
            // Filter projects by ID
            const filteredProjects = allProjects.filter(project => 
                project._id.toLowerCase().includes(searchTerm)
            );
            
            if (categoryView === 'all') {
                // Update the regular list view
                displayProjectsList(filteredProjects);
            } else {
                // Update the client category view
                prepareClientCategoryView(filteredProjects);
            }
        }
        
        function clearSearch() {
            document.getElementById('project-search').value = '';
            filterProjects(); // This will reset to show all projects
        }
        
        function toggleCategoryView() {
            const categoryView = document.getElementById('project-category').value;
            const projectListContainer = document.getElementById('project-list-container');
            const clientCategoryContainer = document.getElementById('client-category-container');
            
            if (categoryView === 'all') {
                projectListContainer.style.display = 'block';
                clientCategoryContainer.style.display = 'none';
            } else {
                projectListContainer.style.display = 'none';
                clientCategoryContainer.style.display = 'block';
            }
            
            // Apply any current search filter
            filterProjects();
        }
        
        function showProjectDetails(projectId) {
            window.location.href = `editproject.html?projectId=${projectId}`;
        }
        
        function goHome() {
            window.location.href = "index.html";
        }
        
        function logout() {
            // Clear all user-related data
            const itemsToClear = ['token', 'username', 'role', 'userAvatar', 'userId'];
            itemsToClear.forEach(item => localStorage.removeItem(item));
            
            // Reset ALL user photos to default before redirecting
            const userPhotos = document.querySelectorAll('.user-photo, #userAvatar');
            userPhotos.forEach(photo => {
                photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
            });
            
            // Redirect to login page
            window.location.href = '/login.html';
        }

        
    // Add this function to update user info including avatar
    function updateUserInterface() {
        const userRole = localStorage.getItem('role');
        const userName = localStorage.getItem('username');
        const userAvatar = localStorage.getItem('userAvatar');
        
        // Update username and role
        const userNameElement = document.querySelector('.user-name');
        const userRoleElement = document.querySelector('.user-role');
        if (userNameElement) userNameElement.textContent = `Welcome, ${userName || 'User'}`;
        if (userRoleElement) userRoleElement.textContent = userRole || 'Role';
        
        // Update all user photos on the page
        const userPhotos = document.querySelectorAll('.user-photo');
        userPhotos.forEach(photo => {
            if (userAvatar) {
                photo.src = userAvatar;
            } else {
                photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'; // Default photo
            }
        });
    }
    
    // Call this function when the page loads
    document.addEventListener('DOMContentLoaded', updateUserInterface);
    
    // Optional: Refresh user interface periodically to catch any changes
    setInterval(updateUserInterface, 30000); // Update every 30 seconds

    // Function to load users by role and populate a dropdown
    async function loadUsersByRole(role, dropdownId, selectedValue) {
        try {
            const token = localStorage.getItem('token');
            let endpoint = '/api/users';
            
            // If your API supports filtering by role, use that endpoint
            if (role === 'Project Manager') {
                endpoint = '/api/users/project-managers';
            } else if (role === 'Creative Service') {
                endpoint = '/api/users/creative-service';
            }
            
            const response = await fetch(endpoint, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error(`Failed to fetch ${role} users`);
            }

            const users = await response.json();
            const dropdown = document.getElementById(dropdownId);
            
            // Keep the first option (placeholder) and remove any existing options
            while (dropdown.options.length > 1) {
                dropdown.remove(1);
            }

            // Filter users by role if the API doesn't support role filtering
            const filteredUsers = endpoint === '/api/users' 
                ? users.filter(user => user.role === role)
                : users;

            // Add users to dropdown
            filteredUsers.forEach(user => {
                const option = document.createElement('option');
                option.value = user.username;
                option.textContent = user.username;
                dropdown.appendChild(option);
            });

            // Set the selected value after options are loaded
            if (selectedValue) {
                dropdown.value = selectedValue;
            }
        } catch (error) {
            console.error(`Error loading ${role} users:`, error);
        }
    }

    async function populateRoleDropdowns(selectedValues = {}) {
        const token = localStorage.getItem("token");
        const response = await fetch('/api/role-users', {
          headers: { Authorization: `Bearer ${token}` }
        });
        const data = await response.json();
        function fillDropdown(dropdownId, users, selectedValue) {
            const dropdown = document.getElementById(dropdownId);
            if (!dropdown) return;
            dropdown.innerHTML = '<option value="">Select...</option>';
            users.forEach(user => {
                const option = document.createElement('option');
                option.value = user.username || user.name || user.email;
                option.textContent = user.username || user.name || user.email;
                dropdown.appendChild(option);
            });
            // Set the value after options are loaded
            if (selectedValue) dropdown.value = selectedValue;
        }
        fillDropdown('projectManagerDropdown', data.projectManager, selectedValues.projectManager);
        fillDropdown('creativeServiceDropdown', data.creativeService, selectedValues.creativeServiceLead);
        fillDropdown('creativeAcquisitionDropdown', data.creativeAcquisition, selectedValues.creativeAcquisitionLead);
        fillDropdown('editorialDropdown', data.editorial, selectedValues.editorialLead);
        fillDropdown('qcDropdown', data.quality, selectedValues.qcLead);
        fillDropdown('digitalLeadDropdown', data.digital, selectedValues.digitalLead);
    }

    async function fetchClients() {
        try {
            const token = localStorage.getItem("token");
            const response = await fetch('/api/clients', {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            
            if (!response.ok) {
                throw new Error("Failed to fetch clients");
            }
            
            const clients = await response.json();
            const clientNameDropdown = document.getElementById("clientNameDropdown");
            
            // Clear existing options
            clientNameDropdown.innerHTML = '<option value="">Select Client</option>';
            
            // Add new options (use clientCode for value and display)
            clients.forEach(client => {
                const option = document.createElement("option");
                option.value = client.clientCode;
                option.textContent = client.clientCode;
                clientNameDropdown.appendChild(option);
            });
        } catch (error) {
            console.error("Error loading clients:", error);
            alert("Error loading clients. Please try again.");
        }
    }
    </script>
</body>
</html>
