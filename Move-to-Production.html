<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Composure Dashboard</title>
    <link rel="stylesheet" href="styles/style.css" />
    <link rel="stylesheet" href="styles/move-to-production.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />
    <!-- Bootstrap CSS -->
    <link
      href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Add Quill CSS and JS -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
  </head>
  <body>
    <div class="container-fluid-movetoproduction"> 
        <div class="row">          
          <div class="main-wrapper-move-to-production">
            <header class="top-bar-wip-report">
              <div class="left-section">
              <button onclick="goHome()" class="home-button">
                <i class="fas fa-home"></i> Home
              </button>
              <!-- Add the dropdowns here -->
              <div class="dropdown-container">
                <div class="dropdown">
                  <button class="dropdown-button">Production</button>
                  <div class="dropdown-content">
                    <a href="Move-to-Production.html">Move to Production</a>
                  </div>
                </div>
                <div class="dropdown">
                  <button class="dropdown-button">Reports</button>
                  <div class="dropdown-content">
                    <a href="WIP-report.html">WIP Report</a>
                  </div>
                </div>
                <div class="dropdown">
                  <button class="dropdown-button">Project Management</button>
                  <div class="dropdown-content">
                    <a href="Invoices.html">Invoice</a>
                  </div>
                </div>
                <div class="dropdown">
                  <button class="dropdown-button">Tickets</button>
                  <div class="dropdown-content">
                    <a href="Tickets.html">Tickets</a>
                  </div>
                </div>
              </div>
            </div>
            <!-- End the dropdowns here -->
            <div class="right-section">
              <div class="user-info">
                <div class="user-photo-container">
                  <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp" alt="User Photo" class="user-photo" onerror="this.onerror=null;this.src='https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';" />
                  <div class="user-dropdown-content">
                    <a href="/user-info.html" onclick="openSettings()"><i class="fas fa-cog"></i> Settings</a>
                  </div>
                </div>
                <div class="user-details">
                  <span class="user-name">Welcome, User</span>
                  <span class="user-role">Role</span>
                </div>
              </div>
                <button onclick="logout()" class="logout-button">Logout</button>
              </div>
            </header>
        </div>
    </div>    

    <div class="container-movetoproduction">
      <h2>Project List - Yet to Start</h2>
      <div class="table-responsive">
        <table class="table table-bordered" id="projects-table">
          <thead>
            <tr>
              <th>Project ID</th>
              <th>Title</th>
              <th>Client Name</th>
              <th>Project Manager</th>
              <th>Start Date</th>
              <th>End Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="projects-table-body">
            <!-- Rows will be dynamically added here -->
          </tbody>
        </table>
      </div>
      <button id="export-button" class="btn btn-primary mt-3">
        <i class="fas fa-file-excel"></i> Export to Excel
      </button>
    </div>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const userNameElement = document.querySelector(".user-name");
        const userRoleElement = document.querySelector(".user-role");
      
        // Retrieve the user's role and name from localStorage
        const userRole = localStorage.getItem("role");
        const userName = localStorage.getItem("username"); // Retrieve the username from localStorage
      
        // Update the user profile section
        if (userNameElement) {
          userNameElement.textContent = `Welcome, ${userName || "User"}`;
        }
        if (userRoleElement) {
          userRoleElement.textContent = userRole || "Role";
        }
      
        console.log("User Role:", userRole); // Debugging
        console.log("User Name:", userName); // Debugging
      });
      </script>
    <script>
        
        function goHome() {
  window.location.href = "index.html";
}

      function logout() {
            // Clear all user-related data
            const itemsToClear = ['token', 'username', 'role', 'userAvatar', 'userId'];
            itemsToClear.forEach(item => localStorage.removeItem(item));
            
            // Reset ALL user photos to default before redirecting
            const userPhotos = document.querySelectorAll('.user-photo, #userAvatar');
            userPhotos.forEach(photo => {
                photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
            });
            
            // Redirect to login page
            window.location.href = '/login.html';
        }

      async function loadProjects() {
        try {
          const token = localStorage.getItem("token"); // Retrieve the token from localStorage or sessionStorage
          if (!token) {
            throw new Error("No authentication token found");
          }

          const response = await fetch("/api/yettostartprojects", {
            headers: {
              Authorization: `Bearer ${token}`, // Include the token in the Authorization header
            },
          });

          if (!response.ok) {
            throw new Error("Failed to fetch projects");
          }

          const projects = await response.json();
          // Sort the projects by Project ID (ascending order)
          projects.sort((a, b) => a._id.localeCompare(b._id));

          const tableBody = document.getElementById("projects-table-body");
          tableBody.innerHTML = ""; // Clear existing rows

          projects.forEach((project) => {
            const row = document.createElement("tr");
            row.innerHTML = `
              <td>${project._id}</td>
              <td>${project.title}</td>
              <td>${project.clientName}</td>
              <td>${project.projectManager || "Not Assigned"}</td>
              <td>${new Date(project.startDate).toLocaleDateString()}</td>
              <td>${new Date(project.endDate).toLocaleDateString()}</td>
              <td>
                <button class="btn btn-primary" onclick="moveToProduction('${project._id}')">Move to Production</button>
                <button class="btn btn-info btn-sm" onclick="editProject('${project._id}')" title="Edit Project">
                  <i class="fas fa-edit"></i>
                </button>
              </td>
            `;
            tableBody.appendChild(row);
          });
        } catch (error) {
          console.error("Error loading projects:", error);
          alert("Error loading projects. Please try again.");
        }
      }

      async function moveToProduction(projectId) {
        const confirmation = confirm("Please confirm the project is ready for Production");
        if (!confirmation) {
          return;
        }
        const token = localStorage.getItem("token"); // ✅ Add this line
  if (!token) {
    alert("No authentication token found. Please log in again.");
    return;
  }

        try {
          const response = await fetch("/api/move-to-production", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({ projectId }),
          });

          if (response.ok) {
            window.location.href = 'index.html?project_moved=true'; // Redirect with success parameter
          } else {
            const error = await response.json();
            alert(`Error: ${error.message}`);
          }
        } catch (error) {
          console.error("Error moving project to production:", error);
          alert("Error moving project to production. Please try again.");
        }
      }

      let editProjectQuill;
      let allTeamMembers = [];

      async function loadTeamMembers() {
        try {
          const token = localStorage.getItem("token");
          const response = await fetch("/api/users/all", {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (!response.ok) {
            throw new Error("Failed to fetch team members");
          }

          allTeamMembers = await response.json();
          
          // Populate the supporting team dropdown
          const supportingTeamSelect = document.getElementById("edit-supportingTeam");
          supportingTeamSelect.innerHTML = "";
          
          allTeamMembers.forEach((member) => {
            const option = document.createElement("option");
            option.value = member._id;
            option.textContent = member.username;
            supportingTeamSelect.appendChild(option);
          });
        } catch (error) {
          console.error("Error loading team members:", error);
        }
      }

      async function editProject(projectId) {
        try {
          const token = localStorage.getItem("token");
          if (!token) {
            alert("No authentication token found. Please log in again.");
            return;
          }

          // Instead of showing a modal, redirect to editproject.html with the project ID
          window.location.href = `editproject.html?projectId=${projectId}&source=yettostart`;
        } catch (error) {
          console.error("Error preparing to edit project:", error);
          alert(`Error preparing to edit project: ${error.message}`);
        }
      }

      // Helper function to format date for input fields
      function formatDateForInput(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toISOString().split('T')[0];
      }

      // Add event listener for supporting team select
      document.addEventListener('DOMContentLoaded', function() {
        const supportingTeamSelect = document.getElementById('edit-supportingTeam');
        if (supportingTeamSelect) {
          supportingTeamSelect.addEventListener('change', function() {
            const selectedValues = Array.from(this.selectedOptions).map(option => option.value);
            document.getElementById('edit-supportingTeam-hidden').value = JSON.stringify(selectedValues);
          });
        }
        
        // Add event listener for save button
        const saveButton = document.getElementById('save-edit-project');
        if (saveButton) {
          saveButton.addEventListener('click', saveProjectChanges);
        }
        
        // Load team members when the page loads
        loadTeamMembers();
      });

      // Function to save project changes
      async function saveProjectChanges() {
        try {
          const projectId = document.getElementById('edit-project-id').value;
          const token = localStorage.getItem('token');
          
          if (!token) {
            alert('No authentication token found. Please log in again.');
            return;
          }
          
          // Get form data
          const projectData = {
            title: document.getElementById('edit-title').value,
            clientName: document.getElementById('edit-clientName').value,
            projectManager: document.getElementById('edit-projectManager').value,
            startDate: document.getElementById('edit-startDate').value,
            endDate: document.getElementById('edit-endDate').value,
            notes: document.getElementById('edit-notes').value
          };
          
          // Get supporting team
          try {
            projectData.supportingTeam = JSON.parse(document.getElementById('edit-supportingTeam-hidden').value || '[]');
          } catch (e) {
            projectData.supportingTeam = [];
          }
          
          // Send update request
          const response = await fetch(`/api/yettostartprojects/${projectId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(projectData)
          });
          
          if (!response.ok) {
            throw new Error('Failed to update project');
          }
          
          // Close the modal
          $('#editProjectModal').modal('hide');
          
          // Reload projects
          loadProjects();
          
          alert('Project updated successfully');
        } catch (error) {
          console.error('Error updating project:', error);
          alert('Error updating project: ' + error.message);
        }
      }

      // Call the function when the page loads
      document.addEventListener("DOMContentLoaded", loadProjects);
      
    </script>
    <script>
      document.getElementById("export-button").addEventListener("click", function () {
        // Get the table element
        const table = document.getElementById("projects-table");

        // Convert the table to a worksheet
        const worksheet = XLSX.utils.table_to_sheet(table);

        // Apply yellow background to header row
        const range = XLSX.utils.decode_range(worksheet['!ref']);
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
          if (!worksheet[cellAddress]) continue;
          
          // Create or update the cell's style
          if (!worksheet[cellAddress].s) worksheet[cellAddress].s = {};
          worksheet[cellAddress].s.fill = {
            patternType: 'solid',
            fgColor: { rgb: 'FFFF00' } // Yellow
          };
          worksheet[cellAddress].s.font = { bold: true };
        }

        // Create a new workbook and append the worksheet
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, "Move to Production");

        // Get the current date and time in IST (Indian Standard Time)
        const now = new Date();
        const istOffset = 5.5 * 60 * 60 * 1000; // IST is UTC+5:30
        const istDate = new Date(now.getTime() + istOffset);
        const currentDateTime = istDate.toISOString().replace("T", "_").split(".")[0].replace(/:/g, "-");

        // Generate the filename with the current date and time in IST
        const filename = `Move_to_Production_${currentDateTime}.xlsx`;

        // Export the workbook to an Excel file
        XLSX.writeFile(workbook, filename);
      });
    </script>
    <script>
      // Add this function to update user info including avatar
      function updateUserInterface() {
          const userRole = localStorage.getItem('role');
          const userName = localStorage.getItem('username');
          const userAvatar = localStorage.getItem('userAvatar');
          
          // Update username and role
          const userNameElement = document.querySelector('.user-name');
          const userRoleElement = document.querySelector('.user-role');
          if (userNameElement) userNameElement.textContent = `Welcome, ${userName || 'User'}`;
          if (userRoleElement) userRoleElement.textContent = userRole || 'Role';
          
          // Update all user photos on the page
          const userPhotos = document.querySelectorAll('.user-photo');
          userPhotos.forEach(photo => {
              if (userAvatar) {
                  photo.src = userAvatar;
              } else {
                  photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'; // Default photo
              }
          });
      }
      
      // Call this function when the page loads
      document.addEventListener('DOMContentLoaded', updateUserInterface);
      
      // Optional: Refresh user interface periodically to catch any changes
      setInterval(updateUserInterface, 30000); // Update every 30 seconds
      </script>
    <!-- Edit Project Modal -->
    <div class="modal fade" id="editProjectModal" tabindex="-1" role="dialog" aria-labelledby="editProjectModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="editProjectModalLabel">Edit Project</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <form id="edit-project-form">
              <input type="hidden" id="edit-project-id">
              <div class="form-row">
                <div class="form-group col-md-6">
                  <label for="edit-title">Title:</label>
                  <input type="text" class="form-control" id="edit-title" name="title" required>
                </div>
                <div class="form-group col-md-6">
                  <label for="edit-clientName">Client Name:</label>
                  <input type="text" class="form-control" id="edit-clientName" name="clientName" required>
                </div>
              </div>
              <div class="form-row">
                <div class="form-group col-md-6">
                  <label for="edit-projectManager">Project Manager:</label>
                  <input type="text" class="form-control" id="edit-projectManager" name="projectManager">
                </div>
                <div class="form-group col-md-6">
                  <label for="edit-supportingTeam">Supporting Team:</label>
                  <select class="form-control" id="edit-supportingTeam" multiple>
                    <!-- Options will be loaded dynamically -->
                  </select>
                  <input type="hidden" id="edit-supportingTeam-hidden" name="supportingTeam">
                </div>
              </div>
              <div class="form-row">
                <div class="form-group col-md-6">
                  <label for="edit-startDate">Start Date:</label>
                  <input type="date" class="form-control" id="edit-startDate" name="startDate" required>
                </div>
                <div class="form-group col-md-6">
                  <label for="edit-endDate">End Date:</label>
                  <input type="date" class="form-control" id="edit-endDate" name="endDate" required>
                </div>
              </div>
              <div class="form-group">
                <label for="edit-notes">Notes:</label>
                <div id="edit-notes-editor" class="quill-editor"></div>
                <input type="hidden" id="edit-notes" name="notes">
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            <button type="button" class="btn btn-primary" id="save-edit-project">Save Changes</button>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
