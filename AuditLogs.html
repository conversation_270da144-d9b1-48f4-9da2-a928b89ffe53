<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audit Logs - WIP Automation</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="styles/style.css">
    <link rel="stylesheet" href="styles/audit-logs.css">
    <link rel="stylesheet" href="styles/additional-settings.css">
</head>
<body>
    <div class="container-fluid">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header"></div>
            <ul class="nav-links">
                <li class="admin-only"><a href="/index.html"><i class="fas fa-home"></i> Dashboard</a></li>
                <li class="admin-only"><a href="/Tickets.html"><i class="fas fa-ticket-alt"></i> Tickets</a></li>
                <li class="admin-only"><a href="/Registration.html"><i class="fas fa-user-plus"></i> User Management</a></li>
                <li class="admin-only"><a href="/additionalsetting.html"><i class="fas fa-cogs"></i> Additional Settings</a></li>
                <li class="admin-only active"><a href="/AuditLogs.html"><i class="fas fa-history"></i> Audit Logs</a></li>
            </ul>
        </nav>
        <!-- Main Content -->
        <div class="row">
            <div class="main-wrapper-audit">
                <header class="top-bar-AuditLogs">
                    <div class="left-section">
                        <button onclick="goHome()" class="home-button">
                            <i class="fas fa-home"></i> Home
                        </button>
                        <div class="dropdown-container">
                            <div class="dropdown">
                                <button class="dropdown-button">Production</button>
                                <div class="dropdown-content">
                                    <a href="/move-to-production.html">Move to Production</a>
                                </div>
                            </div>
                            <div class="dropdown">
                                <button class="dropdown-button">Reports</button>
                                <div class="dropdown-content">
                                    <a href="/wip-report.html">WIP Report</a>
                                </div>
                            </div>
                            <div class="dropdown">
                                <button class="dropdown-button">Project Management</button>
                                <div class="dropdown-content">
                                    <a href="/Invoices.html">Invoice</a>
                                </div>
                            </div>
                            <div class="dropdown">
                                <button class="dropdown-button">Tickets</button>
                                <div class="dropdown-content">
                                    <a href="/Tickets.html">Tickets</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="right-section">
                        <div class="user-info">
                            <div class="user-photo-container">
                                <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp" alt="User Photo" class="user-photo" onerror="this.onerror=null;this.src='https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';" />
                                <div class="user-dropdown-content">
                                    <a href="/user-info.html"><i class="fas fa-cog"></i> Settings</a>
                                    <a href="/AuditLogs.html" class="admin-only"><i class="fas fa-history"></i> Audit Logs</a>
                                </div>
                            </div>
                            <div class="user-details">
                                <span class="user-name">Welcome, User</span>
                                <span class="user-role">Role</span>
                            </div>
                        </div>
                        <button onclick="logout()" class="logout-button">Logout</button>
                    </div>
                </header>
                
                <div class="audit-content">
                    <div class="audit-log-container">
                        <h2><i class="fas fa-history"></i> Audit Logs</h2>
                        <p>Track all changes made to the system</p>
                        
                        <div class="filter-section">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="entityType">Entity Type</label>
                                        <select id="entityType" class="form-control">
                                            <option value="">All Types</option>
                                            <option value="user">User</option>
                                            <option value="project">Project</option>
                                            <option value="ticket">Ticket</option>
                                            <option value="projectStatus">Project Status</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="action">Action</label>
                                        <select id="action" class="form-control">
                                            <option value="">All Actions</option>
                                            <option value="CREATE">Create</option>
                                            <option value="UPDATE">Update</option>
                                            <option value="DELETE">Delete</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="startDate">Start Date</label>
                                        <input type="date" id="startDate" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="endDate">End Date</label>
                                        <input type="date" id="endDate" class="form-control">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="entityId">Entity ID</label>
                                        <input type="text" id="entityId" class="form-control" placeholder="Enter entity ID">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="userId">User</label>
                                        <select id="userId" class="form-control">
                                            <option value="">All Users</option>
                                            <!-- Will be populated dynamically -->
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <button id="applyFilters" class="btn btn-primary">Apply Filters</button>
                            <button id="resetFilters" class="btn btn-secondary ml-2">Reset</button>
                        </div>
                        
                        <div id="logs-container">
                            <!-- Logs will be loaded here -->
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">Loading...</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="pagination-container">
                            <nav aria-label="Audit log pagination">
                                <ul class="pagination justify-content-center" id="pagination">
                                    <!-- Pagination will be generated here -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize Bootstrap components
        $(document).ready(function() {
            // This ensures Bootstrap's JavaScript components are initialized
            $('[data-toggle="collapse"]').on('click', function() {
                const target = $(this).data('target');
                $(target).collapse('toggle');
            });
            
            // Load users for filter dropdown
            loadUsers();
            
            // Load initial logs
            loadAuditLogs();
            
            // Set up event listeners
            $("#applyFilters").click(loadAuditLogs);
            $("#resetFilters").click(resetFilters);
        });
        
        // Function to get auth token
        function getToken() {
            return localStorage.getItem('token');
        }
        
        // Function to check if user is logged in
        function checkAuth() {
            const token = getToken();
            if (!token) {
                window.location.href = 'login.html';
                return false;
            }
            return true;
        }
        
        // Function to make authenticated fetch requests
        async function fetchWithAuth(url, options = {}) {
            if (!checkAuth()) return;
            
            const token = getToken();
            const headers = {
                'Authorization': `Bearer ${token}`,
                ...options.headers
            };
            
            return fetch(url, { ...options, headers });
        }
        
        // Load users for filter dropdown
        async function loadUsers() {
            try {
                const response = await fetchWithAuth('/api/users');
                if (!response.ok) throw new Error('Failed to fetch users');
                
                const users = await response.json();
                const userSelect = document.getElementById('userId');
                
                users.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user._id;
                    option.textContent = user.username;
                    userSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading users:', error);
            }
        }
        
        // Load audit logs with filters
        async function loadAuditLogs(page = 1) {
            try {
                // Get filter values
                const entityType = document.getElementById('entityType').value;
                const action = document.getElementById('action').value;
                const entityId = document.getElementById('entityId').value;
                const userId = document.getElementById('userId').value;
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                
                // Build query string
                let queryParams = new URLSearchParams();
                if (entityType) queryParams.append('entityType', entityType);
                if (action) queryParams.append('action', action);
                if (entityId) queryParams.append('entityId', entityId);
                if (userId) queryParams.append('userId', userId);
                if (startDate) queryParams.append('startDate', startDate);
                if (endDate) queryParams.append('endDate', endDate);
                queryParams.append('page', page);
                queryParams.append('limit', 20);
                
                // Show loading
                document.getElementById('logs-container').innerHTML = `
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                    </div>
                `;
                
                // Fetch logs
                const response = await fetchWithAuth(`/api/audit-logs?${queryParams.toString()}`);
                if (!response.ok) throw new Error('Failed to fetch audit logs');
                
                const data = await response.json();
                renderLogs(data.logs);
                renderPagination(data.pagination);
            } catch (error) {
                console.error('Error loading audit logs:', error);
                document.getElementById('logs-container').innerHTML = `
                    <div class="alert alert-danger">
                        Error loading audit logs: ${error.message}
                    </div>
                `;
            }
        }
        
        // Render logs to the container
        function renderLogs(logs) {
            const container = document.getElementById('logs-container');
            
            if (logs.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-info">
                        No audit logs found matching the current filters.
                    </div>
                `;
                return;
            }
            
            let html = '';
            
            logs.forEach(log => {
                const timestamp = new Date(log.timestamp).toLocaleString();
                const actionClass = getActionClass(log.action);
                
                // Determine what to display as the entity identifier
                let entityDisplay = log.entityId;
                if (log.entityType === 'ticket' && log.additionalInfo) {
                    entityDisplay = log.additionalInfo; // Use ticket number for tickets
                }
                
                html += `
                    <div class="log-entry">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <span class="log-action ${actionClass.toLowerCase()}">${log.action}</span>
                                <span class="ml-2">${log.description}</span>
                            </div>
                            <span class="log-time">${timestamp}</span>
                        </div>
                        <div class="mt-2">
                            <small>
                                <strong>Entity:</strong> ${log.entityType} (${entityDisplay})
                                <span class="ml-3">
                                    <strong>By:</strong> <span class="log-user">${log.performedBy.username}</span>
                                </span>
                            </small>
                        </div>
                        ${renderChanges(log.changes)}
                    </div>
                `;
            });
            
            container.innerHTML = html;
            
            // Initialize any Bootstrap components in the newly added content
            $('[data-toggle="collapse"]').on('click', function() {
                const target = $(this).data('target');
                $(target).collapse('toggle');
            });
        }
        
        // Render changes if available
        function renderChanges(changes) {
            if (!changes || !changes.before || !changes.after) return '';
            
            // Find the differences between before and after
            const differences = findDifferences(changes.before, changes.after);
            if (Object.keys(differences).length === 0) return '';
            
            // Generate a unique ID for this changes section
            const changesId = `changes-${Math.random().toString(36).substr(2, 9)}`;
            
            let html = `
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-secondary" type="button" data-toggle="collapse" data-target="#${changesId}">
                        Show Changes <i class="fas fa-chevron-down ml-1"></i>
                    </button>
                    <div class="collapse mt-2" id="${changesId}">
                        <table class="table table-sm table-bordered changes-table">
                            <thead>
                                <tr>
                                    <th>Field</th>
                                    <th>Before</th>
                                    <th>After</th>
                                </tr>
                            </thead>
                            <tbody>
            `;
            
            for (const [key, value] of Object.entries(differences)) {
                html += `
                    <tr>
                        <td><strong>${formatFieldName(key)}</strong></td>
                        <td>${formatValue(value.before)}</td>
                        <td>${formatValue(value.after)}</td>
                    </tr>
                `;
            }
            
            html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
            
            return html;
        }
        
        // Find differences between two objects
        function findDifferences(before, after) {
            const differences = {};
            
            // Skip if either object is null or undefined
            if (!before || !after) {
                if (before) {
                    // Object was deleted
                    for (const key in before) {
                        if (key === '_id' || key === '__v' || key === 'createdAt' || key === 'updatedAt') continue;
                        differences[key] = {
                            before: before[key],
                            after: null
                        };
                    }
                } else if (after) {
                    // Object was created
                    for (const key in after) {
                        if (key === '_id' || key === '__v' || key === 'createdAt' || key === 'updatedAt') continue;
                        differences[key] = {
                            before: null,
                            after: after[key]
                        };
                    }
                }
                return differences;
            }
            
            // Check all keys in before
            for (const key in before) {
                // Skip internal MongoDB fields
                if (key === '_id' || key === '__v' || key === 'createdAt' || key === 'updatedAt') continue;
                
                // Check if the value is different
                if (JSON.stringify(before[key]) !== JSON.stringify(after[key])) {
                    differences[key] = {
                        before: before[key],
                        after: after[key] || null
                    };
                }
            }
            
            // Check for new keys in after
            for (const key in after) {
                if (key === '_id' || key === '__v' || key === 'createdAt' || key === 'updatedAt') continue;
                
                if (!(key in before)) {
                    differences[key] = {
                        before: undefined,
                        after: after[key]
                    };
                }
            }
            
            return differences;
        }
        
        // Format field name for display
        function formatFieldName(name) {
            return name
                .replace(/([A-Z])/g, ' $1') // Insert a space before all capital letters
                .replace(/^./, str => str.toUpperCase()); // Capitalize the first letter
        }
        
        // Format value for display
        function formatValue(value) {
            if (value === undefined || value === null) return '<em>None</em>';
            
            if (typeof value === 'object') {
                if (value instanceof Date || (typeof value === 'string' && !isNaN(Date.parse(value)))) {
                    // Handle date objects or date strings
                    return new Date(value).toLocaleString();
                }
                
                // For arrays or objects, stringify with indentation
                try {
                    return `<pre class="mb-0" style="max-height: 100px; overflow-y: auto;">${JSON.stringify(value, null, 2)}</pre>`;
                } catch (e) {
                    return String(value);
                }
            }
            
            if (typeof value === 'boolean') {
                return value ? 'Yes' : 'No';
            }
            
            // Handle strings that might be dates
            if (typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)) {
                try {
                    return new Date(value).toLocaleString();
                } catch (e) {
                    return value;
                }
            }
            
            return String(value);
        }
        
        // Get CSS class for action type
        function getActionClass(action) {
            switch (action.toUpperCase()) {
                case 'CREATE': return 'create';
                case 'UPDATE': return 'update';
                case 'DELETE': return 'delete';
                default: return '';
            }
        }
        
        // Render pagination controls
        function renderPagination(pagination) {
            const paginationEl = document.getElementById('pagination');
            paginationEl.innerHTML = '';
            
            if (!pagination || pagination.pages <= 1) return;
            
            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${pagination.page === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `
                <a class="page-link" href="#" aria-label="Previous" ${pagination.page > 1 ? `onclick="loadAuditLogs(${pagination.page - 1}); return false;"` : ''}>
                    <span aria-hidden="true">&laquo;</span>
                </a>
            `;
            paginationEl.appendChild(prevLi);
            
            // Page numbers
            const startPage = Math.max(1, pagination.page - 2);
            const endPage = Math.min(pagination.pages, pagination.page + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                const pageLi = document.createElement('li');
                pageLi.className = `page-item ${i === pagination.page ? 'active' : ''}`;
                pageLi.innerHTML = `
                    <a class="page-link" href="#" onclick="loadAuditLogs(${i}); return false;">${i}</a>
                `;
                paginationEl.appendChild(pageLi);
            }
            
            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${pagination.page === pagination.pages ? 'disabled' : ''}`;
            nextLi.innerHTML = `
                <a class="page-link" href="#" aria-label="Next" ${pagination.page < pagination.pages ? `onclick="loadAuditLogs(${pagination.page + 1}); return false;"` : ''}>
                    <span aria-hidden="true">&raquo;</span>
                </a>
            `;
            paginationEl.appendChild(nextLi);
        }
        
        // Reset all filters
        function resetFilters() {
            document.getElementById('entityType').value = '';
            document.getElementById('action').value = '';
            document.getElementById('entityId').value = '';
            document.getElementById('userId').value = '';
            document.getElementById('startDate').value = '';
            document.getElementById('endDate').value = '';
            
            loadAuditLogs();
        }
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/login.html';
                return;
            }
            
            // Check if user is admin
            const userRole = localStorage.getItem('role');
            if (userRole !== 'Admin') {
                alert('Access denied. Only administrators can view audit logs.');
                window.location.href = '/index.html';
                return;
            }
            
            // Update user info in the navbar if it exists
            const userNameElement = document.querySelector(".user-name");
            const userRoleElement = document.querySelector(".user-role");
            const userName = localStorage.getItem("username");
            
            if (userNameElement) {
                userNameElement.textContent = `Welcome, ${userName || "User"}`;
            }
            if (userRoleElement) {
                userRoleElement.textContent = userRole || "Role";
            }
            
            // Update user photo if available
            const userAvatar = localStorage.getItem('userAvatar');
            if (userAvatar) {
                const userPhotos = document.querySelectorAll('.user-photo');
                userPhotos.forEach(photo => {
                    photo.src = userAvatar;
                    // Fallback to default if image fails to load
                    photo.onerror = () => {
                        photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
                    };
                });
            }
        });

        // Add logout function
        function logout() {
            // Clear all user-related data
            const itemsToClear = ['token', 'username', 'role', 'userAvatar', 'userId'];
            itemsToClear.forEach(item => localStorage.removeItem(item));
            
            // Reset ALL user photos to default before redirecting
            const userPhotos = document.querySelectorAll('.user-photo');
            userPhotos.forEach(photo => {
                photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
            });
            
            // Redirect to login page
            window.location.href = '/login.html';
        }
    </script>
    <script>
             function goHome() {
  window.location.href = "index.html";
}
    </script>
    <script>
        // Show sidebar links for admin users
        document.addEventListener('DOMContentLoaded', function() {
            const userRole = localStorage.getItem('role');
            const adminElements = document.querySelectorAll('.admin-only');
            adminElements.forEach(element => {
                if (userRole === 'Admin') {
                    element.style.display = 'block';
                } else {
                    element.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
