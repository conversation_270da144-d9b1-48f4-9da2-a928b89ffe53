<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add New Project</title>
    <link rel="stylesheet" href="styles/style.css">
    <!-- Bootstrap CSS -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Add this to the head section of your HTML -->
    <link rel="stylesheet" href="styles/new-project.css">
    <!-- Add Quill CSS and JS -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <style>
        #new-project-form button[type="submit"] {
            margin-top: 24px !important; /* Creates clear separation */
        }
        
        /* Override Quill container height to prevent layout issues */
        .ql-container {
            height: auto !important;
        }
        
        /* Supporting Team Custom Dropdown Styles */
        .supporting-team-dropdown {
            position: relative; /* This is crucial for z-index to work */
            z-index: 1050; /* A high z-index, common for modals/dropdowns */
        }
        
        .supporting-team-menu {
            /* Bootstrap handles positioning, we just need the parent to have a z-index */
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="sidebar">
                <h2>Dashboards</h2>
                <hr class="divider">
                <nav>
                    <h3>PROJECT LIST</h3>
                    <ul id="project-list">
                        <!-- Project items will be loaded here -->
                    </ul>
                </nav>
                <!--<button onclick="window.location.href='/new-project.html'">Add New Project</button> -->
            </div>
            <div class="main-wrapper">
                <header class="top-bar">
                  <button onclick="goHome()" class="home-button">
                    <i class="fas fa-home"></i> Home
                  </button>
                  <div class="user-info">
                    <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp" alt="User Photo" class="user-photo" onerror="this.onerror=null;this.src='https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';" />
                    <div class="user-details">
                      <span class="user-name">Welcome, User</span>
                      <span class="user-role">Role</span>
                    </div>
                    <button onclick="logout()" class="logout-button">Logout</button>
                  </div>
                </header>
                <div class="main-content" id="main-content">
                    <div class="project-create-container" style="padding: 20px; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);">
                        <h2 class="text-center mb-4">Project Creation</h2>
                        <form id="new-project-form" class="form-container">
                            <div class="project-details-content">
                                <!-- Project ID -->
                                <div class="project-details-box">
                                    <label for="project-id">Project ID:</label>
                                    <input type="text" class="form-control" id="project-id" name="projectId" required>
                                    <small class="form-text text-muted">A unique identifier for this project.</small>
                                </div>
                                <!-- Project Title -->
                                <div class="project-details-box">
                                    <label for="project-title">Project Title:</label>
                                    <input type="text" class="form-control" id="project-title" name="title" required>
                                    <small class="form-text text-muted">The full, official title of the project.</small>
                                </div>
                                <!-- Client Name -->
                                <div class="project-details-box">
                                    <label for="client-name">Client Name:</label>
                                    <select class="form-control" id="clientNameDropdown" name="clientName" required>
                                        <option value="">Select Client</option>
                                    </select>
                                    <small class="form-text text-muted">Choose the client from the list.</small>
                                </div>
                                <!-- Starting Date -->
                                <div class="project-details-box">
                                    <label for="start-date">Starting Date:</label>
                                    <input type="date" class="form-control" id="start-date" name="startDate" required>
                                    <small class="form-text text-muted">The official start date of the project.</small>
                                </div>
                                <!-- End Date -->
                                <div class="project-details-box">
                                    <label for="end-date">End Date:</label>
                                    <input type="date" class="form-control" id="end-date" name="endDate" required>
                                    <small class="form-text text-muted">The expected completion or due date.</small>
                                </div>
                                <!-- Project Manager -->
                                <div class="project-details-box">
                                    <label for="project-manager">Project Manager Name:</label>
                                    <select class="form-control" id="projectManagerDropdown" name="projectManager" required>
                                        <option value="">Select Project Manager</option>
                                    </select>
                                    <small class="form-text text-muted">The primary manager for this project.</small>
                                </div>
                                <!-- Creative Service Lead -->
                                <div class="project-details-box">
                                    <label for="creative-service-lead">Creative Service Lead Name:</label>
                                    <select class="form-control" id="creativeServiceDropdown" name="creativeServiceLead" required>
                                        <option value="">Select Creative Service Lead</option>
                                    </select>
                                    <small class="form-text text-muted">Lead for creative services.</small>
                                </div>
                                <!-- Creative Acquisition Lead -->
                                <div class="project-details-box">
                                    <label for="creative-acquisition-lead">Creative Acquisition Lead Name:</label>
                                    <select class="form-control" id="creativeAcquisitionDropdown" name="creativeAcquisitionLead" required>
                                        <option value="">Select Creative Acquisition Lead</option>
                                    </select>
                                    <small class="form-text text-muted">Lead for creative acquisition.</small>
                                </div>
                                <!-- Editorial Lead -->
                                <div class="project-details-box">
                                    <label for="editorial-lead">Editorial Lead:</label>
                                    <select class="form-control" id="editorialDropdown" name="editorialLead">
                                        <option value="">Select Editorial Lead</option>
                                    </select>
                                    <small class="form-text text-muted">The main contact for editorial.</small>
                                </div>
                                <!-- QC Lead -->
                                <div class="project-details-box">
                                    <label for="qc-lead">QC Lead:</label>
                                    <select class="form-control" id="qcDropdown" name="qcLead">
                                        <option value="">Select QC Lead</option>
                                    </select>
                                    <small class="form-text text-muted">The lead for quality control.</small>
                                </div>
                                <!-- Page Count -->
                                <div class="project-details-box">
                                    <label for="page-count">Page Count:</label>
                                    <input type="number" class="form-control" id="page-count" name="pageCount" required>
                                    <small class="form-text text-muted">Total number of pages in the project.</small>
                                </div>
                                <!-- ISBN -->
                                <div class="project-details-box">
                                    <label for="isbn">ISBN:</label>
                                    <div id="isbn-container">
                                        <div class="isbn-entry input-group mb-2">
                                            <input type="text" class="form-control isbn-input" name="isbn[]" placeholder="Enter ISBN">
                                            <div class="input-group-append">
                                                <button type="button" class="btn btn-success add-isbn-btn"><i class="fas fa-plus"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">Add one or more ISBNs.</small>
                                </div>
                                <!-- Trim Size -->
                                <div class="project-details-box">
                                    <label for="trim-size">Trim Size:</label>
                                    <input type="text" class="form-control" id="trim-size" name="trimSize" required>
                                    <small class="form-text text-muted">e.g., 8.5 x 11 in</small>
                                </div>
                                <!-- Color -->
                                <div class="project-details-box">
                                    <label for="color">Color:</label>
                                    <select id="color" name="color" class="form-control" required>
                                        <option value="1/C">1/C</option>
                                        <option value="4/C">4/C</option>
                                    </select>
                                    <small class="form-text text-muted">Select the color profile.</small>
                                </div>
                                <!-- Bleed -->
                                <div class="project-details-box">
                                    <label for="bleed">Bleed:</label>
                                    <input type="text" class="form-control" id="bleed" name="bleed">
                                    <small class="form-text text-muted">Specify bleed requirements (e.g., 0.125 in).</small>
                                </div>
                                <!-- Combined Digital Project Box -->
                                <div class="project-details-box">
                                    <div class="form-group mb-2">
                                        <label for="is-digital-project">Is this a digital project?</label>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="is-digital-project" name="isDigitalProject">
                                            <label class="form-check-label" for="is-digital-project">Yes</label>
                                        </div>
                                        <small class="form-text text-muted">Check if this is primarily a digital product.</small>
                                    </div>
                                    <div class="form-group mb-0" id="digital-lead-row" style="display:none;">
                                        <label for="digital-lead">Digital Lead:</label>
                                        <select class="form-control" id="digitalLeadDropdown" name="digitalLead">
                                            <option value="">Select Digital Lead</option>
                                        </select>
                                        <small class="form-text text-muted">Assign the lead for digital components.</small>
                                    </div>
                                </div>
                                <!-- Levels -->
                                <div class="project-details-box">
                                    <label for="levels">Levels:</label>
                                    <input type="number" class="form-control" id="levels" name="levels" required>
                                    <small class="form-text text-muted">Number of levels or books in the project</small>
                                </div>
                                <!-- Units -->
                                <div class="project-details-box">
                                    <label for="units">Units:</label>
                                    <input type="number" class="form-control" id="units" name="units" required>
                                    <small class="form-text text-muted">Number of units or chapters per level or book.</small>
                                </div>
                                <!-- New wrapper for side-by-side layout -->
                                <div class="d-flex w-100" style="gap: 10px;">
                                    <!-- Supporting Team -->
                                    <div class="project-details-box" style="flex: 1;">
                                        <label for="supporting-team">Supporting Team:</label>
                                        <div class="supporting-team-dropdown dropdown">
                                            <button class="supporting-team-btn form-control d-flex justify-content-between align-items-center" type="button" id="supportingTeamDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <span class="selected-text">Supporting Team Members</span>
                                                <i class="fas fa-chevron-down"></i>
                                            </button>
                                            <div class="supporting-team-menu dropdown-menu w-100 p-3 shadow-sm" aria-labelledby="supportingTeamDropdown">
                                                <div class="search-container mb-2">
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                                        <input type="text" class="form-control" id="supportingTeamSearch" placeholder="Search team members..." onclick="event.stopPropagation()">
                                                    </div>
                                                </div>
                                                <div id="supportingTeamOptions" class="team-options-container" onclick="event.stopPropagation()"></div>
                                            </div>
                                        </div>
                                        <div id="selectedMembersBox" class="selected-members-box"></div>
                                        <input type="hidden" id="supportingTeam" name="supportingTeam">
                                        <small class="form-text text-muted">Select additional team members.</small>
                                    </div>
                                    <!-- Notes -->
                                    <div class="project-details-box" style="flex: 1;">
                                        <label for="notes">Notes:</label>
                                        <div id="notes-editor" class="quill-editor"></div>
                                        <input type="hidden" id="notes" name="notes">
                                        <small class="form-text text-muted">Add any important project notes or details.</small>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary btn-block mt-4">Create Project</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteConfirmationModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfirmationModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteConfirmationModalLabel">Confirm Delete</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete this project? This action cannot be undone.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
document.addEventListener("DOMContentLoaded", () => {
    const token = localStorage.getItem("token"); // Retrieve the token
    if (!token) {
      alert("Unauthorized access. Please log in.");
      window.location.href = "/login.html";
    }
});

document.addEventListener('DOMContentLoaded', () => {
    const token = localStorage.getItem('token');

    if (!token) {
        alert('Unauthorized access. Please login again.');
        window.location.href = '/login.html';
        return;
    }

    loadProjects(token);
    
    // Initialize Quill editor for notes
    const notesQuill = new Quill('#notes-editor', {
        theme: 'snow',
        modules: {
            toolbar: [
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                ['link', 'image'],
                ['clean']
            ]
        },
        placeholder: 'Add any additional notes about the project...'
    });
    
    // Update hidden input when editor content changes
    notesQuill.on('text-change', function() {
        document.getElementById('notes').value = notesQuill.root.innerHTML;
    });

    // Load team members for the supporting team dropdown
    loadTeamMembers();
    
    // Set up search functionality
    document.getElementById('supportingTeamSearch').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        document.querySelectorAll('#supportingTeamOptions .form-check').forEach(item => {
            const username = item.querySelector('label span').textContent.toLowerCase();
            const role = item.querySelector('label small').textContent.toLowerCase();
            if (username.includes(searchTerm) || role.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });

    // Set up checkbox change events
    document.querySelectorAll('#supportingTeamOptions .form-check-input').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedTeamMembers();
        });
    });

    // Function to update the selected team members display
    function updateSelectedTeamMembers() {
        const selectedMembers = Array.from(
            document.querySelectorAll('#supportingTeamOptions input[type="checkbox"]:checked')
        ).map(cb => cb.dataset.username);

        const selectedText = document.querySelector('.supporting-team-btn .selected-text');
        if (selectedText) {
            if (selectedMembers.length === 0) {
                selectedText.textContent = 'Select Supporting Team Members';
            } else if (selectedMembers.length <= 2) {
                selectedText.textContent = selectedMembers.join(', ');
            } else {
                selectedText.textContent = `${selectedMembers.length} members selected`;
            }
        }

        // Update hidden input value
        document.getElementById('supportingTeam').value = JSON.stringify(selectedMembers);
        // Update selected members box
        const selectedMembersBox = document.getElementById('selectedMembersBox');
        if (selectedMembersBox) {
            selectedMembersBox.innerHTML = '';
            selectedMembers.forEach(username => {
                const memberTag = document.createElement('span');
                memberTag.className = 'selected-member-tag';
                memberTag.innerHTML = `
                    ${username}
                    <i class="fas fa-times remove-member" onclick="removeTeamMember('${username}')"></i>
                `;
                selectedMembersBox.appendChild(memberTag);
            });
        }
    }
    
    // Global function to remove team member
    window.removeTeamMember = function(username) {
        const checkbox = document.querySelector(`#supportingTeamOptions input[data-username="${username}"]`);
        if (checkbox) {
            checkbox.checked = false;
            updateSelectedTeamMembers();
        }
    };

    // Initialize supporting team dropdown
    loadTeamMembers();
    
    // Handle dropdown toggle
    document.getElementById('supportingTeamDropdown').addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        document.querySelector('.supporting-team-menu').classList.toggle('show');
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        const dropdown = document.querySelector('.supporting-team-dropdown');
        if (!dropdown.contains(event.target)) {
            document.querySelector('.supporting-team-menu').classList.remove('show');
        }
    });

    document.getElementById('new-project-form').addEventListener('submit', async function(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const projectData = {};
        
        formData.forEach((value, key) => {
            projectData[key] = key === 'projectId' ? String(value).trim() : value;
        });

        // Add this code to collect all ISBN values
    const isbnInputs = document.querySelectorAll('.isbn-input');
    const isbnValues = Array.from(isbnInputs)
        .map(input => input.value.trim())
        .filter(value => value !== '');
    projectData.isbns = isbnValues;
        
        // Handle supporting team (parse from JSON string back to array)
        if (document.getElementById('supportingTeam').value) {
            try {
                projectData.supportingTeam = JSON.parse(document.getElementById('supportingTeam').value);
            } catch (e) {
                projectData.supportingTeam = [];
            }
        } else {
            projectData.supportingTeam = [];
        }
        
        // Get notes from Quill editor
        projectData.notes = document.getElementById('notes').value;

        // Fix isDigitalProject value to Boolean
        projectData.isDigitalProject = document.getElementById('is-digital-project').checked;

        try {
            const token = localStorage.getItem('token');
            const response = await fetch('/api/yettostartprojects', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`
                },
                body: JSON.stringify(projectData)
            });

            if (!response.ok) {
                let errorMessage = "Unknown error";
                try {
                    const errorData = await response.json();
                    if (errorData.error) {
                        // Custom backend error (e.g., duplicate project ID)
                        errorMessage = errorData.error;
                    } else if (errorData.errors && Array.isArray(errorData.errors)) {
                        // express-validator errors
                        errorMessage = errorData.errors.map(e => e.msg).join('\n');
                    } else if (errorData.message) {
                        // Fallback for other error formats
                        errorMessage = errorData.message;
                    }
                } catch (e) {
                    // If response is not JSON, keep "Unknown error"
                }
                alert("Error creating project:\n" + errorMessage);
                return;
            }

            window.location.href = "index.html?project_created=true"; // Redirect with success parameter
        } catch (error) {
            console.error('Error creating project:', error);
            alert('Error creating project');
        }
    });

    const userNameElement = document.querySelector(".user-name");
    const userRoleElement = document.querySelector(".user-role");

    // Retrieve the user's role and name from localStorage
    const userRole = localStorage.getItem("role");
    const userName = localStorage.getItem("username"); // Retrieve the username from localStorage

    // Update the user profile section
    if (userNameElement) {
      userNameElement.textContent = `Welcome, ${userName || "User"}`;
    }
    if (userRoleElement) {
      userRoleElement.textContent = userRole || "Role";
    }

    console.log("User Role:", userRole); // Debugging
    console.log("User Name:", userName); // Debugging
});

async function loadProjects(token) {
    const projectList = document.getElementById('project-list');
    projectList.innerHTML = '';
    const userRole = localStorage.getItem("role");
    const username = localStorage.getItem("username");

    try {
        const response = await fetch('/api/yettostartprojects', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        const projects = await response.json();
        
        // Filter projects based on user role
        let filteredProjects = projects;
        
        if (userRole !== "Admin") {
            // Filter projects where the user is involved
            filteredProjects = projects.filter(project => {
                return (
                    project.projectManager === username || 
                    project.creativeServiceLead === username || 
                    project.creativeAcquisitionLead === username ||
                    (Array.isArray(project.supportingTeam) && project.supportingTeam.includes(username))
                );
            });
        }
        
        filteredProjects.forEach((project) => {
            const li = document.createElement('li');
            li.innerHTML = `<a href="#">${project._id} - ${project.title}</a>`;
            projectList.appendChild(li);
        });
    } catch (error) {
        console.error('Failed to load projects:', error);
    }
}

        function showDeleteConfirmation(id) {
            projectToDelete = id;
            $('#deleteConfirmationModal').modal('show');
        }

        document.getElementById('confirmDeleteBtn').addEventListener('click', async function() {
            if (!projectToDelete) return;

            try {
                const response = await fetch(`http://localhost:3000/api/yettostartprojects/${projectToDelete}`, {
                    method: 'DELETE'
                    
                });

                if (response.ok) {
                    $('#deleteConfirmationModal').modal('hide');
                    loadProjects(); // Reload the project list
                } else {
                    alert('Failed to delete project');
                }
            } catch (error) {
                console.error('Error deleting project:', error);
                alert('Error deleting project');
            } finally {
                projectToDelete = null;
            }
        });

        function goHome() {
  window.location.href = "index.html";
}

        function logout() {
            // Clear all user-related data
            const itemsToClear = ['token', 'username', 'role', 'userAvatar', 'userId'];
            itemsToClear.forEach(item => localStorage.removeItem(item));
            
            // Reset ALL user photos to default before redirecting
            const userPhotos = document.querySelectorAll('.user-photo, #userAvatar');
            userPhotos.forEach(photo => {
                photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
            });
            
            // Redirect to login page
            window.location.href = '/login.html';
        }

        
    </script>
    <script>
        // Add this function to update user info including avatar
        function updateUserInterface() {
            const userRole = localStorage.getItem('role');
            const userName = localStorage.getItem('username');
            const userAvatar = localStorage.getItem('userAvatar');
            
            // Update username and role
            const userNameElement = document.querySelector('.user-name');
            const userRoleElement = document.querySelector('.user-role');
            if (userNameElement) userNameElement.textContent = `Welcome, ${userName || 'User'}`;
            if (userRoleElement) userRoleElement.textContent = userRole || 'Role';
            
            // Update all user photos on the page
            const userPhotos = document.querySelectorAll('.user-photo');
            userPhotos.forEach(photo => {
                if (userAvatar) {
                    photo.src = userAvatar;
                } else {
                    photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'; // Default photo
                }
            });
        }
        
// Add this code right after your existing DOMContentLoaded event listener
// or inside it if you prefer
document.addEventListener('DOMContentLoaded', function() {
    // Your existing code...
    
    // Add event listener for the "Add ISBN" button
    document.querySelector('.add-isbn-btn').addEventListener('click', function() {
        const isbnContainer = document.getElementById('isbn-container');
        const newIsbnEntry = document.createElement('div');
        newIsbnEntry.className = 'isbn-entry input-group mb-2';
        newIsbnEntry.innerHTML = `
            <input type="text" class="form-control isbn-input" name="isbn[]" placeholder="Enter ISBN">
            <div class="input-group-append">
                <button type="button" class="btn btn-danger remove-isbn-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        // Add event listener to the remove button
        const removeButton = newIsbnEntry.querySelector('.remove-isbn-btn');
        removeButton.addEventListener('click', function() {
            isbnContainer.removeChild(newIsbnEntry);
        });
        
        isbnContainer.appendChild(newIsbnEntry);
    });
});        

        // Call this function when the page loads
        document.addEventListener('DOMContentLoaded', updateUserInterface);
        
        // Optional: Refresh user interface periodically to catch any changes
        setInterval(updateUserInterface, 30000); // Update every 30 seconds
        </script>
    </script>
    <script>
        // Function to load users by role and populate a dropdown
        async function loadUsersByRole(role, dropdownId) {
            try {
                const token = localStorage.getItem('token');
                let endpoint = '/api/users';
                
                // If your API supports filtering by role, use that endpoint
                if (role === 'Project Manager') {
                    endpoint = '/api/users/project-managers';
                } else if (role === 'Creative Service') {
                    endpoint = '/api/users/creative-service';
                }
                
                const response = await fetch(endpoint, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`Failed to fetch ${role} users`);
                }

                const users = await response.json();
                const dropdown = document.getElementById(dropdownId);
                
                // Keep the first option (placeholder) and remove any existing options
                while (dropdown.options.length > 1) {
                    dropdown.remove(1);
                }

                // Filter users by role if the API doesn't support role filtering
                const filteredUsers = endpoint === '/api/users' 
                    ? users.filter(user => user.role === role)
                    : users;

                // Add users to dropdown
                filteredUsers.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.username;
                    option.textContent = user.username;
                    dropdown.appendChild(option);
                });
            } catch (error) {
                console.error(`Error loading ${role} users:`, error);
            }
        }

        // Add this function to load team members for the supporting team dropdown
        async function loadTeamMembers() {
            try {
                const token = localStorage.getItem('token');
                const response = await fetch('/api/users', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch team members');
                }

                const users = await response.json();
                const optionsContainer = document.getElementById('supportingTeamOptions');
                
                // Clear existing options
                optionsContainer.innerHTML = '';
                
                // Add each user as a checkbox option
                users.forEach(user => {
                    const option = document.createElement('div');
                    option.className = 'form-check';
                    option.innerHTML = `
                        <input class="form-check-input" type="checkbox" id="user-${user.username}" 
                               data-username="${user.username}" data-role="${user.role || 'User'}">
                        <label class="form-check-label" for="user-${user.username}">
                            <span>${user.username}</span>
                            <small class="text-muted d-block">${user.role || 'User'}</small>
                        </label>
                    `;
                    optionsContainer.appendChild(option);
                });
                
                // Set up checkbox change events
                document.querySelectorAll('#supportingTeamOptions .form-check-input').forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        updateSelectedTeamMembers();
                    });
                });
            } catch (error) {
                console.error('Error loading team members:', error);
            }
        }
    </script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // ... existing code ...
        // Toggle Digital Lead field
        const isDigitalCheckbox = document.getElementById('is-digital-project');
        const digitalLeadRow = document.getElementById('digital-lead-row');
        isDigitalCheckbox.addEventListener('change', function() {
            digitalLeadRow.style.display = this.checked ? '' : 'none';
        });
    });
    </script>
    <script>
    async function populateRoleDropdowns() {
        const token = localStorage.getItem("token");
        const response = await fetch('/api/role-users', {
            headers: { Authorization: `Bearer ${token}` }
        });
        if (!response.ok) {
            alert("Session expired or unauthorized. Please log in again.");
            window.location.href = "/login.html";
            return;
        }
        const data = await response.json();
        function fillDropdown(dropdownId, users) {
            const dropdown = document.getElementById(dropdownId);
            if (!dropdown) return;
            dropdown.innerHTML = '<option value="">Select...</option>';
            users.forEach(user => {
                const option = document.createElement('option');
                option.value = user.username || user.name || user.email;
                option.textContent = user.username || user.name || user.email;
                dropdown.appendChild(option);
            });
        }
        fillDropdown('projectManagerDropdown', data.projectManager);
        fillDropdown('creativeServiceDropdown', data.creativeService);
        fillDropdown('creativeAcquisitionDropdown', data.creativeAcquisition);
        fillDropdown('editorialDropdown', data.editorial);
        fillDropdown('qcDropdown', data.quality);
        fillDropdown('digitalLeadDropdown', data.digital);
    }
    document.addEventListener('DOMContentLoaded', populateRoleDropdowns);
    </script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // ... existing code ...
        // Fetch clients and populate the dropdown
        fetchClients();
    });

    async function fetchClients() {
        try {
            const token = localStorage.getItem("token");
            const response = await fetch('/api/clients', {
                headers: { Authorization: `Bearer ${token}` }
            });

            if (!response.ok) {
                alert("Session expired or unauthorized. Please log in again.");
                window.location.href = "/login.html";
                return;
            }
            const clients = await response.json();
            if (!Array.isArray(clients)) {
                console.error("Clients response is not an array:", clients);
                return;
            }
            const clientNameDropdown = document.getElementById('clientNameDropdown');
            clientNameDropdown.innerHTML = '<option value="">Select Client</option>';
            clients.forEach(client => {
                const option = document.createElement('option');
                option.value = client.clientCode;
                option.textContent = client.clientCode;
                clientNameDropdown.appendChild(option);
            });
        } catch (error) {
            console.error('Error fetching clients:', error);
        }
    }
    </script>
    <script>
    function updateSelectedTeamMembers(preselectedTeam = null) {
        const selectedMembers = [];
        const checkboxes = document.querySelectorAll("#supportingTeamOptions input[type='checkbox']");

        // If preselected team is provided, check those checkboxes
        if (preselectedTeam && Array.isArray(preselectedTeam)) {
            checkboxes.forEach(checkbox => {
                const username = checkbox.dataset.username;
                if (preselectedTeam.includes(username)) {
                    checkbox.checked = true;
                    selectedMembers.push(username);
                } else {
                    checkbox.checked = false;
                }
            });
        } else {
            // Otherwise, get selected checkboxes
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    selectedMembers.push(checkbox.dataset.username);
                }
            });
        }

        // Update hidden input
        document.getElementById("supportingTeam").value = JSON.stringify(selectedMembers);

        // Update selected members display
        const selectedTeamContainer = document.getElementById('selectedMembersBox');
        selectedTeamContainer.innerHTML = "";

        if (selectedMembers.length > 0) {
            selectedMembers.forEach(member => {
                const badge = document.createElement("span");
                badge.className = "selected-member-tag";
                badge.innerHTML = `${member} <i class="fas fa-times remove-member" onclick="removeTeamMember('${member}')"></i>`;
                selectedTeamContainer.appendChild(badge);
            });
        } else {
            selectedTeamContainer.innerHTML = "<p class='text-muted'>No team members selected</p>";
        }

        // Update dropdown button text
        const selectedText = document.querySelector('.supporting-team-btn .selected-text');
        if (selectedText) {
            if (selectedMembers.length === 0) {
                selectedText.textContent = 'Select Supporting Team Members';
            } else if (selectedMembers.length <= 2) {
                selectedText.textContent = selectedMembers.join(', ');
            } else {
                selectedText.textContent = `${selectedMembers.length} members selected`;
            }
        }
    }

    // Make removeTeamMember global
    window.removeTeamMember = function(username) {
        const checkbox = document.querySelector(`#supportingTeamOptions input[data-username="${username}"]`);
        if (checkbox) {
            checkbox.checked = false;
            updateSelectedTeamMembers();
        }
    };
    </script>
</body>
</html>


